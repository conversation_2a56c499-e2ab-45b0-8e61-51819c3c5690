import collections
import re
import os
import xlwt


def mkdir(path):
    path = path.strip()
    path = path.rstrip("\\")
    isExists = os.path.exists(path)

    if not isExists:
        os.makedirs(path)


def AnalyzeText(text_file, keywords):
    text = ""
    with open(text_file, encoding='utf8') as file:
        text = file.read()
    text = re.sub('[^\u4e00-\u9fa5]', '', text)

    stat = {}
    for key in keywords:
        stat[key] = text.count(key)

    return stat


def AnalyzeTextWithContext(text_file, keywords, context_length=200):
    """分析文本并提取关键词上下文"""
    with open(text_file, encoding='utf8') as file:
        original_text = file.read()

    # 清理文本用于统计
    cleaned_text = re.sub('[^\u4e00-\u9fa5]', '', original_text)

    stat = {}
    contexts = {}

    for keyword in keywords:
        count = cleaned_text.count(keyword)
        stat[keyword] = count

        # 如果找到关键词，提取上下文
        if count > 0:
            keyword_contexts = extract_keyword_context(original_text, keyword, context_length)
            contexts[keyword] = keyword_contexts

    return stat, contexts


def extract_keyword_context(content, keyword, context_length=200):
    """提取关键词的上下文片段"""
    contexts = []
    start = 0

    while True:
        # 查找关键词位置
        pos = content.find(keyword, start)
        if pos == -1:
            break

        # 计算上下文范围 - 增加上下文长度
        context_start = max(0, pos - context_length)
        context_end = min(len(content), pos + len(keyword) + context_length)

        # 提取上下文
        context = content[context_start:context_end]

        # 清理上下文中的换行符和多余空格，但保留基本格式
        context = re.sub(r'\s+', ' ', context.strip())

        # 标记关键词位置
        keyword_start_in_context = context.find(keyword)
        if keyword_start_in_context == -1:
            # 如果在清理后的文本中找不到关键词，跳过
            start = pos + 1
            continue

        keyword_end_in_context = keyword_start_in_context + len(keyword)

        # 格式化上下文，突出显示关键词
        formatted_context = (
            context[:keyword_start_in_context] +
            f"【{keyword}】" +
            context[keyword_end_in_context:]
        )

        # 确保上下文不会太长，限制在500字符以内
        if len(formatted_context) > 500:
            # 如果太长，从关键词位置向两边截取
            keyword_pos_in_formatted = formatted_context.find(f"【{keyword}】")
            start_pos = max(0, keyword_pos_in_formatted - 200)
            end_pos = min(len(formatted_context), keyword_pos_in_formatted + 200)
            formatted_context = "..." + formatted_context[start_pos:end_pos] + "..."

        contexts.append({
            'context': formatted_context,
            'position': pos,
            'keyword': keyword,
            'length': len(formatted_context)
        })

        start = pos + 1

    return contexts


def SetStyle(name, height, bold=False):
    style = xlwt.XFStyle()
    font = xlwt.Font()
    font.name = name
    font.bold = bold
    font.color_index = 4
    font.height = height
    style.font = font
    return style


def StatWriter(file_path, keywords, stats):
    xls = xlwt.Workbook()

    sheet = xls.add_sheet('Sheet', cell_overwrite_ok=True)
    row0 = [''] + keywords
    colum0 = list(stats.keys())

    for i in range(0, len(row0)):
        sheet.write(0, i, row0[i])
    for i in range(0, len(colum0)):
        sheet.write(i + 1, 0, colum0[i])
    for i in range(0, len(colum0)):
        for j in range(1, len(row0)):
            sheet.write(i + 1, j, stats[colum0[i]][row0[j]])

    xls.save(file_path)


def MergeDict(a, b):
    for key in a:
        a[key] += b[key]
    return a
