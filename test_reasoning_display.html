<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度思考内容显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
    
    <style>
        /* 深度思考内容样式 */
        #reasoningSection {
            background-color: #f8f9fa;
        }

        #reasoningStreamingContent {
            min-height: 50px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.6;
            font-size: 0.95em;
            color: #495057;
        }

        /* 折叠按钮动画 */
        #reasoningToggleIcon {
            transition: transform 0.3s ease;
        }

        .collapsed #reasoningToggleIcon {
            transform: rotate(0deg);
        }

        .not-collapsed #reasoningToggleIcon {
            transform: rotate(90deg);
        }

        /* AI内容样式 */
        .ai-content-display {
            min-height: 200px;
            white-space: pre-wrap;
            word-wrap: break-word;
            line-height: 1.6;
        }

        /* Markdown样式 */
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .markdown-content p {
            margin-bottom: 0.8rem;
        }

        .markdown-content ul, .markdown-content ol {
            margin-bottom: 0.8rem;
            padding-left: 1.5rem;
        }

        .markdown-content li {
            margin-bottom: 0.3rem;
        }

        .markdown-content code {
            background-color: #e9ecef;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-size: 0.875em;
        }

        .markdown-content pre {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
            overflow-x: auto;
        }

        .markdown-content blockquote {
            border-left: 4px solid #007bff;
            padding-left: 1rem;
            margin: 1rem 0;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>深度思考内容显示测试</h2>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <h4>测试控制</h4>
                <div class="btn-group" role="group">
                    <button class="btn btn-primary" onclick="simulateNormalResponse()">模拟普通AI响应</button>
                    <button class="btn btn-success" onclick="simulateReasoningResponse()">模拟带深度思考的响应</button>
                    <button class="btn btn-warning" onclick="clearAll()">清空所有内容</button>
                </div>
            </div>
            <div class="col-md-6">
                <h4>视图控制</h4>
                <button class="btn btn-outline-primary" onclick="toggleMarkdownView()">
                    <i id="markdownIcon" class="bi bi-file-text"></i>
                    <span id="markdownText">纯文本</span>
                </button>
            </div>
        </div>
        
        <!-- 模拟AI分析结果容器 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightbulb"></i> AI分析结果</h5>
            </div>
            <div class="card-body p-0" style="height: 600px; overflow-y: auto;">
                <!-- 深度思考内容区域 (可折叠，默认折叠) -->
                <div id="reasoningSection" class="border-bottom" style="display: none;">
                    <div class="p-3 bg-light border-bottom">
                        <button class="btn btn-link text-decoration-none p-0 w-100 text-start collapsed" 
                                type="button" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#reasoningContent" 
                                aria-expanded="false" 
                                aria-controls="reasoningContent">
                            <i class="bi bi-brain me-2 text-primary"></i>
                            <strong>深度思考内容</strong>
                            <i class="bi bi-chevron-right float-end" id="reasoningToggleIcon"></i>
                        </button>
                    </div>
                    <div class="collapse" id="reasoningContent">
                        <div class="p-3">
                            <div id="reasoningStreamingContent" class="reasoning-content">
                                <!-- 深度思考内容将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI分析内容 -->
                <div class="p-3">
                    <div id="aiStreamingContent" class="ai-content-display">
                        点击上方按钮开始测试...
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <h5>状态信息</h5>
            <div class="alert alert-info">
                <strong>普通内容长度:</strong> <span id="normalContentLength">0</span><br>
                <strong>深度思考内容长度:</strong> <span id="reasoningContentLength">0</span><br>
                <strong>当前视图模式:</strong> <span id="currentViewMode">Markdown</span><br>
                <strong>深度思考区域显示:</strong> <span id="reasoningSectionDisplay">隐藏</span>
            </div>
        </div>
    </div>

    <script>
        // 模拟AI分析状态
        let testState = {
            isMarkdownView: true,
            rawContent: '',
            reasoningContent: ''
        };

        // 模拟safeMarkdownParse函数
        function safeMarkdownParse(content) {
            try {
                return marked.parse(content);
            } catch (e) {
                console.error('Markdown解析失败:', e);
                return '<pre>' + content.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>';
            }
        }

        function updateStatus() {
            document.getElementById('normalContentLength').textContent = testState.rawContent.length;
            document.getElementById('reasoningContentLength').textContent = testState.reasoningContent.length;
            document.getElementById('currentViewMode').textContent = testState.isMarkdownView ? 'Markdown' : '纯文本';
            
            const reasoningSection = document.getElementById('reasoningSection');
            document.getElementById('reasoningSectionDisplay').textContent = 
                reasoningSection.style.display === 'none' ? '隐藏' : '显示';
        }

        function simulateNormalResponse() {
            clearAll();
            
            const normalContent = `# AI分析报告

## 协同创新关系分析

基于年报内容分析，发现以下重要信息：

### 主要发现
1. **技术合作**: 公司与多家高校建立了产学研合作关系
2. **创新投入**: 研发投入占营收比例持续增长  
3. **合作伙伴**: 与腾讯、华为等公司在AI领域深度合作

### 投资建议
建议重点关注公司的技术创新能力和合作伙伴关系的发展。

**风险提示**: 请注意技术合作的稳定性和市场竞争变化。`;

            testState.rawContent = normalContent;
            updateDisplay();
            updateStatus();
        }

        function simulateReasoningResponse() {
            clearAll();
            
            const normalContent = `# AI分析报告

## 协同创新关系分析

基于年报内容分析，发现以下重要信息：

### 主要发现
1. **技术合作**: 公司与多家高校建立了产学研合作关系
2. **创新投入**: 研发投入占营收比例持续增长
3. **合作伙伴**: 与腾讯、华为等公司在AI领域深度合作

### 投资建议
建议重点关注公司的技术创新能力和合作伙伴关系的发展。`;

            const reasoningContent = `## 分析思路

这是一个复杂的分析问题，需要从多个维度考虑：

### 数据质量评估
- **年报数据的完整性**: 检查所有相关年报是否完整下载
- **关键词匹配的精确度**: 确保关键词匹配算法的准确性
- **上下文提取的相关性**: 验证提取的上下文是否真正相关

### 分析方法论
1. **识别合作关系**: 首先识别所有相关的合作关系
2. **评估合作深度**: 然后评估合作的深度和广度
3. **影响价值判断**: 最后判断对公司价值的影响

### 风险因素考虑
- **合作关系的稳定性**: 评估长期合作的可持续性
- **技术依赖风险**: 分析对特定技术或合作伙伴的依赖程度
- **市场竞争变化**: 考虑行业竞争格局的变化

### 结论置信度
基于当前数据，分析结论的置信度约为**85%**，建议结合更多外部数据进行验证。

> 注意：此分析基于有限的年报数据，实际投资决策应结合更多信息源。`;

            testState.rawContent = normalContent;
            testState.reasoningContent = reasoningContent;
            
            // 显示深度思考区域
            const reasoningSection = document.getElementById('reasoningSection');
            reasoningSection.style.display = 'block';
            
            updateDisplay();
            updateStatus();
        }

        function updateDisplay() {
            const streamingContentDiv = document.getElementById('aiStreamingContent');
            const reasoningStreamingContent = document.getElementById('reasoningStreamingContent');
            
            // 更新普通内容
            if (testState.isMarkdownView) {
                const markdownHtml = safeMarkdownParse(testState.rawContent);
                streamingContentDiv.innerHTML = markdownHtml;
                streamingContentDiv.classList.add('markdown-content');
            } else {
                streamingContentDiv.textContent = testState.rawContent;
                streamingContentDiv.classList.remove('markdown-content');
            }
            
            // 更新深度思考内容
            if (testState.reasoningContent) {
                if (testState.isMarkdownView) {
                    const reasoningMarkdownHtml = safeMarkdownParse(testState.reasoningContent);
                    reasoningStreamingContent.innerHTML = reasoningMarkdownHtml;
                    reasoningStreamingContent.classList.add('markdown-content');
                } else {
                    reasoningStreamingContent.textContent = testState.reasoningContent;
                    reasoningStreamingContent.classList.remove('markdown-content');
                }
            }
        }

        function toggleMarkdownView() {
            const markdownIcon = document.getElementById('markdownIcon');
            const markdownText = document.getElementById('markdownText');
            
            testState.isMarkdownView = !testState.isMarkdownView;
            
            if (testState.isMarkdownView) {
                markdownIcon.className = 'bi bi-file-text';
                markdownText.textContent = '纯文本';
            } else {
                markdownIcon.className = 'bi bi-markdown';
                markdownText.textContent = 'Markdown';
            }
            
            updateDisplay();
            updateStatus();
        }

        function clearAll() {
            testState.rawContent = '';
            testState.reasoningContent = '';
            
            document.getElementById('aiStreamingContent').innerHTML = '点击上方按钮开始测试...';
            document.getElementById('reasoningStreamingContent').innerHTML = '';
            document.getElementById('reasoningSection').style.display = 'none';
            
            updateStatus();
        }

        // 深度思考内容折叠按钮动画
        document.addEventListener('DOMContentLoaded', function() {
            const reasoningToggle = document.querySelector('[data-bs-target="#reasoningContent"]');
            const reasoningToggleIcon = document.getElementById('reasoningToggleIcon');
            
            if (reasoningToggle && reasoningToggleIcon) {
                reasoningToggle.addEventListener('click', function() {
                    setTimeout(() => {
                        if (this.classList.contains('collapsed')) {
                            reasoningToggleIcon.style.transform = 'rotate(0deg)';
                        } else {
                            reasoningToggleIcon.style.transform = 'rotate(90deg)';
                        }
                    }, 50);
                });
            }
            
            // 初始化状态
            updateStatus();
        });
    </script>
</body>
</html>
