<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown流式解析测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
    <style>
        .markdown-content {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        }
        
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .markdown-content p {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }
        
        .markdown-content ul,
        .markdown-content ol {
            margin-bottom: 0.75rem;
            padding-left: 1.5rem;
        }
        
        .markdown-content li {
            margin-bottom: 0.15rem;
        }
        
        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 0.75rem 0;
        }
        
        .markdown-content th,
        .markdown-content td {
            border: 1px solid #e9ecef;
            padding: 0.5rem;
            text-align: left;
        }
        
        .markdown-content th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .typing-cursor {
            display: inline-block;
            width: 2px;
            height: 1.2em;
            background-color: #007bff;
            animation: blink 1s infinite;
            margin-left: 2px;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        @keyframes fadeIn {
            0% { opacity: 0; transform: translateY(-10px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        .content-display {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            background-color: #fff;
        }
        
        .status-info {
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="bi bi-markdown"></i> Markdown流式解析测试</h1>
                <p class="text-muted">测试流式响应中的markdown格式转换问题修复效果</p>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 测试控制</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">选择测试内容:</label>
                            <select id="testContent" class="form-select">
                                <option value="simple">简单文本</option>
                                <option value="markdown">Markdown格式</option>
                                <option value="table">表格内容</option>
                                <option value="mixed">混合内容</option>
                                <option value="ai_report">AI分析报告</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">流式速度 (毫秒/字符):</label>
                            <input type="range" id="streamSpeed" class="form-range" min="10" max="200" value="50">
                            <small class="text-muted">当前: <span id="speedValue">50</span>ms</small>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enableMarkdown" checked>
                                <label class="form-check-label" for="enableMarkdown">
                                    启用Markdown解析
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button id="startTest" class="btn btn-primary">
                                <i class="bi bi-play"></i> 开始测试
                            </button>
                            <button id="stopTest" class="btn btn-danger" disabled>
                                <i class="bi bi-stop"></i> 停止测试
                            </button>
                            <button id="clearContent" class="btn btn-secondary">
                                <i class="bi bi-trash"></i> 清空内容
                            </button>
                        </div>
                        
                        <div class="mt-3 status-info">
                            <div><strong>状态:</strong> <span id="testStatus">就绪</span></div>
                            <div><strong>内容长度:</strong> <span id="contentLength">0</span> 字符</div>
                            <div><strong>解析次数:</strong> <span id="parseCount">0</span></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-eye"></i> 显示效果</h5>
                        <div>
                            <button id="toggleView" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-markdown"></i> 切换视图
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div id="contentDisplay" class="content-display"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-bug"></i> 调试信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="debugInfo" class="bg-light p-3 rounded" style="font-family: monospace; font-size: 0.9rem; max-height: 200px; overflow-y: auto;">
                            等待测试开始...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试内容数据
        const testContents = {
            simple: "这是一个简单的文本测试。\n\n包含多个段落。\n\n用于测试基本的流式显示功能。",
            
            markdown: `# 标题测试
            
## 二级标题

这是一个**粗体文本**和*斜体文本*的测试。

### 列表测试

- 第一项
- 第二项
- 第三项

### 代码测试

\`\`\`javascript
function test() {
    console.log("Hello World");
}
\`\`\`

> 这是一个引用块测试`,

            table: `# 表格测试

| 公司代码 | 公司名称 | 是否协同创新 |
|---|---|---|
| 000001 | 平安银行 | 是 |
| 000002 | 万科A | 否 |
| 600036 | 招商银行 | 是 |

## 分析结果

上述表格展示了测试数据。`,

            mixed: `# 混合内容测试

## 1. 基本信息

**公司名称:** 测试公司
**股票代码:** 000000

## 2. 分析结果

### 2.1 数据表格

| 项目 | 数值 | 说明 |
|---|---|---|
| 营收 | 100亿 | 同比增长10% |
| 利润 | 20亿 | 同比增长15% |

### 2.2 代码示例

\`\`\`python
def analyze_data(data):
    return data.sum()
\`\`\`

## 3. 结论

- 业绩良好
- 增长稳定
- 前景乐观`,

            ai_report: `**分析报告：**

**测试公司 (000000) 2024年年报中关联方协同创新情况**

**1. 是否与关联方存在协同创新：**

是的，存在协同创新关系。

**2. 详细分析表**

| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |
|---|---|---|
| 测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作，共同推进产品创新。 |
| 子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同，提升整体竞争力。 |
| 子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目。 |

**3. 协同创新具体表现：**

- **技术研发合作：** 公司与关联方建立联合研发中心
- **资源共享：** 共享研发设备和人才资源  
- **市场协同：** 在产品推广方面形成合力

**4. 总结：**

该公司在关联方协同创新方面表现积极，通过技术合作和资源整合，有效提升了整体创新能力。`
        };
        
        // 全局状态
        let testState = {
            isRunning: false,
            currentContent: '',
            isMarkdownView: true,
            parseCount: 0,
            streamInterval: null
        };

        // 配置marked.js
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                breaks: true,
                gfm: true,
                sanitize: false,
                smartLists: true,
                smartypants: false,
                xhtml: false,
                pedantic: false,
                silent: false
            });

            const renderer = new marked.Renderer();

            renderer.table = function(header, body) {
                if (body) body = '<tbody>' + body + '</tbody>';
                return '<div class="table-responsive"><table class="table table-striped table-bordered">\n'
                    + '<thead>\n'
                    + header
                    + '</thead>\n'
                    + body
                    + '</table></div>\n';
            };

            renderer.paragraph = function(text) {
                if (!text || text.trim() === '') {
                    return '';
                }
                return '<p>' + text + '</p>\n';
            };

            marked.setOptions({ renderer: renderer });
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeTest();
        });

        function initializeTest() {
            // 绑定事件
            document.getElementById('startTest').addEventListener('click', startTest);
            document.getElementById('stopTest').addEventListener('click', stopTest);
            document.getElementById('clearContent').addEventListener('click', clearContent);
            document.getElementById('toggleView').addEventListener('click', toggleView);

            // 速度滑块
            const speedSlider = document.getElementById('streamSpeed');
            const speedValue = document.getElementById('speedValue');
            speedSlider.addEventListener('input', function() {
                speedValue.textContent = this.value;
            });

            updateStatus();
        }

        function startTest() {
            if (testState.isRunning) return;

            const contentType = document.getElementById('testContent').value;
            const speed = parseInt(document.getElementById('streamSpeed').value);
            const enableMarkdown = document.getElementById('enableMarkdown').checked;

            testState.isRunning = true;
            testState.currentContent = '';
            testState.parseCount = 0;
            testState.isMarkdownView = enableMarkdown;

            document.getElementById('startTest').disabled = true;
            document.getElementById('stopTest').disabled = false;

            clearContent();
            updateStatus('运行中');

            const fullContent = testContents[contentType];
            simulateStreaming(fullContent, speed);
        }

        function stopTest() {
            if (!testState.isRunning) return;

            testState.isRunning = false;

            if (testState.streamInterval) {
                clearInterval(testState.streamInterval);
                testState.streamInterval = null;
            }

            document.getElementById('startTest').disabled = false;
            document.getElementById('stopTest').disabled = true;

            updateStatus('已停止');

            // 进行最终解析
            if (testState.isMarkdownView && testState.currentContent) {
                finalParse();
            }
        }

        function clearContent() {
            testState.currentContent = '';
            testState.parseCount = 0;

            const display = document.getElementById('contentDisplay');
            display.innerHTML = '';

            updateStatus();
            debugLog('内容已清空');
        }

        function toggleView() {
            testState.isMarkdownView = !testState.isMarkdownView;

            const button = document.getElementById('toggleView');
            const display = document.getElementById('contentDisplay');

            if (testState.isMarkdownView) {
                button.innerHTML = '<i class="bi bi-file-text"></i> 纯文本';
                if (testState.currentContent) {
                    try {
                        const html = safeMarkdownParse(testState.currentContent);
                        display.innerHTML = html;
                        display.classList.add('markdown-content');
                    } catch (e) {
                        display.textContent = testState.currentContent;
                        display.classList.remove('markdown-content');
                    }
                }
            } else {
                button.innerHTML = '<i class="bi bi-markdown"></i> Markdown';
                display.textContent = testState.currentContent;
                display.classList.remove('markdown-content');
            }

            updateStatus();
        }

        function updateStatus(status = '就绪') {
            document.getElementById('testStatus').textContent = status;
            document.getElementById('contentLength').textContent = testState.currentContent.length;
            document.getElementById('parseCount').textContent = testState.parseCount;
        }

        function debugLog(message) {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}\n`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
        }

        // 流式处理函数
        function simulateStreaming(content, speed) {
            let index = 0;
            const display = document.getElementById('contentDisplay');

            // 添加光标
            const cursor = document.createElement('span');
            cursor.className = 'typing-cursor';
            cursor.textContent = '|';

            debugLog(`开始流式传输，内容长度: ${content.length}，速度: ${speed}ms/字符`);

            testState.streamInterval = setInterval(() => {
                if (!testState.isRunning || index >= content.length) {
                    stopTest();
                    return;
                }

                // 添加字符
                const char = content[index];
                testState.currentContent += char;
                index++;

                // 移除光标
                if (cursor.parentNode) {
                    cursor.parentNode.removeChild(cursor);
                }

                // 更新显示
                if (testState.isMarkdownView) {
                    updateMarkdownDisplay();
                } else {
                    display.textContent = testState.currentContent;
                    display.classList.remove('markdown-content');
                }

                // 重新添加光标
                display.appendChild(cursor);

                // 滚动到底部
                display.scrollTop = display.scrollHeight;

                // 更新状态
                if (index % 50 === 0) {
                    updateStatus('流式传输中');
                }

            }, speed);
        }

        // 更新Markdown显示
        function updateMarkdownDisplay() {
            const display = document.getElementById('contentDisplay');

            try {
                const shouldParse = shouldParseMarkdownNow(testState.currentContent);

                if (shouldParse) {
                    debugLog(`触发Markdown解析，内容长度: ${testState.currentContent.length}`);
                    const html = safeMarkdownParse(testState.currentContent);
                    display.innerHTML = html;
                    display.classList.add('markdown-content');
                    testState.parseCount++;
                } else {
                    // 使用格式化文本
                    const formatted = formatStreamingText(testState.currentContent);
                    display.innerHTML = formatted;
                    display.classList.remove('markdown-content');
                }
            } catch (e) {
                debugLog(`Markdown解析失败: ${e.message}`);
                const formatted = formatStreamingText(testState.currentContent);
                display.innerHTML = formatted;
                display.classList.remove('markdown-content');
            }
        }

        // 最终解析
        function finalParse() {
            if (!testState.currentContent) return;

            const display = document.getElementById('contentDisplay');

            try {
                debugLog('开始最终Markdown解析');
                const html = safeMarkdownParse(testState.currentContent);
                display.innerHTML = html;
                display.classList.add('markdown-content');
                testState.parseCount++;

                // 代码高亮
                display.querySelectorAll('pre code').forEach((block) => {
                    try {
                        hljs.highlightElement(block);
                    } catch (e) {
                        debugLog(`代码高亮失败: ${e.message}`);
                    }
                });

                debugLog('最终Markdown解析完成');

                // 添加完成提示
                const indicator = document.createElement('div');
                indicator.className = 'alert alert-success mt-2 fade-in';
                indicator.innerHTML = '<i class="bi bi-check-circle"></i> 解析完成';
                indicator.style.fontSize = '0.8rem';
                indicator.style.padding = '0.25rem 0.5rem';
                display.appendChild(indicator);

                setTimeout(() => {
                    if (indicator.parentNode) {
                        indicator.style.opacity = '0';
                        setTimeout(() => {
                            if (indicator.parentNode) {
                                indicator.parentNode.removeChild(indicator);
                            }
                        }, 300);
                    }
                }, 3000);

            } catch (e) {
                debugLog(`最终解析失败: ${e.message}`);
                const formatted = formatStreamingText(testState.currentContent);
                display.innerHTML = formatted;
                display.classList.add('markdown-content');
            }

            updateStatus('解析完成');
        }

        // 智能判断是否应该解析markdown
        function shouldParseMarkdownNow(content) {
            if (!content || content.length < 50) return false;

            // 检查内容长度 - 每500字符解析一次，避免过于频繁
            const parseInterval = 500;
            const shouldParseByLength = content.length % parseInterval < 50;

            // 检查是否包含完整的markdown结构
            const hasCompleteStructures = checkForCompleteMarkdownStructures(content);

            // 检查是否以完整的句子结束
            const endsWithCompleteSentence = checkSentenceCompleteness(content);

            // 如果内容很长（>2000字符），强制解析以提供视觉反馈
            const isLongContent = content.length > 2000;

            return shouldParseByLength || hasCompleteStructures || endsWithCompleteSentence || isLongContent;
        }

        // 检查完整的markdown结构
        function checkForCompleteMarkdownStructures(content) {
            // 检查是否有完整的段落（以双换行结束）
            if (content.includes('\n\n')) return true;

            // 检查是否有完整的标题行
            if (content.match(/^#+\s.+$/m)) return true;

            // 检查是否有完整的列表项
            if (content.match(/^[-*+]\s.+$/m)) return true;

            // 检查是否有完整的表格行
            if (content.match(/\|.+\|$/m)) return true;

            return false;
        }

        // 检查句子完整性
        function checkSentenceCompleteness(content) {
            const trimmedContent = content.trim();
            if (trimmedContent.length < 10) return false;

            const lastChar = trimmedContent[trimmedContent.length - 1];
            const completeEndings = ['.', '。', '!', '！', '?', '？', ':', '：', ')', '）', ']', '】', '}'];

            return completeEndings.includes(lastChar);
        }

        // 格式化流式文本显示
        function formatStreamingText(content) {
            if (!content) return '';

            try {
                // 基本的HTML格式化，保持换行和基本结构
                return content
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/\n\n/g, '</p><p>')
                    .replace(/\n/g, '<br>')
                    .replace(/^/, '<p>')
                    .replace(/$/, '</p>')
                    // 简单的粗体格式
                    .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
                    // 简单的标题格式
                    .replace(/^<p>(#+)\s*([^<]+)<\/p>/gm, (match, hashes, title) => {
                        const level = Math.min(hashes.length, 6);
                        return `<h${level}>${title}</h${level}>`;
                    });
            } catch (e) {
                debugLog(`格式化流式文本失败: ${e.message}`);
                return '<pre>' + content.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>';
            }
        }

        // 安全的markdown解析函数
        function safeMarkdownParse(content) {
            try {
                if (!content || typeof content !== 'string') {
                    return '';
                }

                // 基础清理
                let processedContent = content
                    .replace(/[\u200B-\u200D\uFEFF\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, '')
                    .replace(/\r\n/g, '\n')
                    .replace(/\r/g, '\n')
                    .trim();

                // AI内容格式化
                processedContent = formatAIContentSimple(processedContent);

                // 表格修复
                processedContent = fixTableFormatSimple(processedContent);

                // 最终清理
                processedContent = finalCleanup(processedContent);

                if (processedContent.length < 5) {
                    return '<p>' + escapeHtml(processedContent) + '</p>';
                }

                const parsed = marked.parse(processedContent);

                if (!parsed || parsed.trim() === '') {
                    return '<pre class="bg-light p-3 border rounded">' + escapeHtml(processedContent) + '</pre>';
                }

                return parsed;

            } catch (error) {
                debugLog(`Markdown解析失败: ${error.message}`);
                const safeContent = (content || '').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                return `
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Markdown解析失败</h6>
                        <p>错误信息：${escapeHtml(error.message)}</p>
                    </div>
                    <pre class="bg-light p-3 border rounded" style="white-space: pre-wrap;">${safeContent}</pre>
                `;
            }
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 简化的AI内容格式化函数
        function formatAIContentSimple(content) {
            if (!content) return '';

            try {
                return content
                    .replace(/\*\*([^*]+分析报告[^*]*)\*\*/g, '\n# $1\n')
                    .replace(/\*\*([^*]{5,50})[：:]\s*\*\*/g, '\n## $1\n')
                    .replace(/^(\d+\.\s*[^：\n]{3,30})[：:]\s*/gm, '\n### $1\n\n')
                    .replace(/([。！？])\s*([^。！？\n\|]{15,})/g, '$1\n\n$2')
                    .replace(/\*\*\s*\*\*/g, '')
                    .replace(/\*\*([^*]+)\*\*/g, '**$1**')
                    .replace(/\n{3,}/g, '\n\n')
                    .trim();
            } catch (e) {
                debugLog(`AI内容格式化失败: ${e.message}`);
                return content;
            }
        }

        // 简化的表格格式修复函数
        function fixTableFormatSimple(content) {
            if (!content) return '';

            try {
                return content
                    .replace(/\*\*年报主体公司[\/\\]代码\*\*\s*\|\s*\*\*是否存在协同创新\*\*\s*\|\s*\*\*原文数据\*\*/g,
                        '\n| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |\n|---|---|---|')
                    .replace(/\|\s*([^|\n]+?)\s*\|\s*([^|\n]+?)\s*\|\s*([^|\n]*?)\s*\|/g, '| $1 | $2 | $3 |')
                    .replace(/\|\s*[-\-]+\s*\|\s*[-\-]+\s*\|\s*[-\-]+\s*\|/g, '|---|---|---|')
                    .replace(/\*\*\|\s*/g, '| ')
                    .replace(/\s*\|\s*\*\*/g, ' |')
                    .replace(/\|\s+/g, '| ')
                    .replace(/\s+\|/g, ' |');
            } catch (e) {
                debugLog(`表格格式修复失败: ${e.message}`);
                return content;
            }
        }

        // 最终清理函数
        function finalCleanup(content) {
            if (!content) return '';

            try {
                return content
                    .replace(/<script[^>]*>.*?<\/script>/gi, '')
                    .replace(/<style[^>]*>.*?<\/style>/gi, '')
                    .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
                    .replace(/^(#{1,6})\s*(.*)$/gm, '$1 $2')
                    .replace(/\n{3,}/g, '\n\n')
                    .replace(/^\s+/gm, '')
                    .replace(/\s+$/gm, '')
                    .trim();
            } catch (e) {
                debugLog(`最终清理失败: ${e.message}`);
                return content;
            }
        }
    </script>
</body>
</html>
