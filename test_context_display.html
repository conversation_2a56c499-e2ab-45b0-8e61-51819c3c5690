<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分析上下文显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>AI分析上下文显示测试</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试控制</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary mb-2" onclick="testContextDisplay()">测试上下文显示</button>
                        <button class="btn btn-info mb-2" onclick="debugAiContextState()">调试上下文状态</button>
                        <button class="btn btn-warning mb-2" onclick="clearContext()">清空上下文</button>
                        <button class="btn btn-success mb-2" onclick="simulateStreamResponse()">模拟流式响应</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>状态信息</h5>
                    </div>
                    <div class="card-body">
                        <div id="statusInfo">
                            <p>点击测试按钮开始...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- AI分析结果区域 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>AI分析结果</h5>
                    </div>
                    <div class="card-body">
                        <!-- 引用上下文 -->
                        <div id="contextSection" style="display: none;">
                            <div class="p-3 bg-light border-bottom">
                                <div class="d-flex justify-content-between align-items-center">
                                    <button class="btn btn-link text-decoration-none p-0 flex-grow-1 text-start" type="button"
                                            data-bs-toggle="collapse" data-bs-target="#contextReferences"
                                            aria-expanded="false" aria-controls="contextReferences">
                                        <i class="bi bi-chevron-right" id="contextToggleIcon"></i>
                                        <i class="bi bi-quote"></i>
                                        引用上下文 (<span id="contextCount">0</span>)
                                    </button>
                                    <button class="btn btn-sm btn-outline-info" type="button" onclick="debugAiContextState()" title="调试上下文状态">
                                        <i class="bi bi-bug"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="collapse" id="contextReferences">
                                <div class="p-3" id="contextContent">
                                    <!-- 搜索到的上下文将在这里显示 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟全局变量
        let aiContextData = {
            withKeywords: [],
            relatedPartyOnly: [],
            currentWithKeywordsPage: 1,
            currentRelatedPartyPage: 1,
            pageSize: 5
        };

        // 测试数据
        const testContexts = [
            {
                stock_code: "000001",
                company_name: "平安银行",
                related_party: "腾讯",
                context: "公司与腾讯在AI技术方面开展深度合作，共同推进人工智能技术的应用。",
                keywords_found: ["AI", "人工智能"],
                has_keywords: true,
                context_type: "with_keywords"
            },
            {
                stock_code: "000002",
                company_name: "万科A",
                related_party: "Microsoft",
                context: "Microsoft Corporation是我们的重要合作伙伴，在云计算领域有密切协作。",
                keywords_found: ["Microsoft"],
                has_keywords: true,
                context_type: "with_keywords"
            },
            {
                stock_code: "000001",
                company_name: "平安银行",
                related_party: "阿里巴巴",
                context: "与阿里巴巴集团在金融科技领域建立了战略合作关系。",
                keywords_found: [],
                has_keywords: false,
                context_type: "related_party_only"
            }
        ];

        function testContextDisplay() {
            updateStatus("开始测试上下文显示...");
            
            try {
                displayContextReferences(testContexts);
                updateStatus("✅ 上下文显示测试完成");
            } catch (error) {
                updateStatus("❌ 上下文显示测试失败: " + error.message);
                console.error(error);
            }
        }

        function clearContext() {
            const contextSection = document.getElementById('contextSection');
            const contextContent = document.getElementById('contextContent');
            const contextCount = document.getElementById('contextCount');
            
            if (contextSection) contextSection.style.display = 'none';
            if (contextContent) contextContent.innerHTML = '';
            if (contextCount) contextCount.textContent = '0';
            
            aiContextData = {
                withKeywords: [],
                relatedPartyOnly: [],
                currentWithKeywordsPage: 1,
                currentRelatedPartyPage: 1,
                pageSize: 5
            };
            
            updateStatus("上下文已清空");
        }

        function simulateStreamResponse() {
            updateStatus("模拟流式响应...");
            
            // 模拟接收上下文数据
            setTimeout(() => {
                const data = {
                    type: 'contexts',
                    data: testContexts
                };
                
                handleStreamData(data);
                updateStatus("✅ 流式响应模拟完成");
            }, 1000);
        }

        function handleStreamData(data) {
            console.log('收到流式数据:', data);
            
            const contextSection = document.getElementById('contextSection');
            const contextCountSpan = document.getElementById('contextCount');

            switch (data.type) {
                case 'contexts':
                    console.log('🔍 处理上下文数据:', data.data);
                    
                    // 验证上下文数据
                    if (!data.data || !Array.isArray(data.data)) {
                        console.error('❌ 上下文数据格式错误:', data.data);
                        updateStatus('上下文数据格式错误');
                        break;
                    }
                    
                    const contexts = data.data;
                    console.log(`📊 上下文数据统计: 总数=${contexts.length}`);
                    
                    // 分析上下文类型分布
                    const withKeywords = contexts.filter(ctx => ctx.context_type === 'with_keywords');
                    const relatedPartyOnly = contexts.filter(ctx => ctx.context_type === 'related_party_only');
                    console.log(`📊 上下文类型分布: 包含关键词=${withKeywords.length}, 仅关联方=${relatedPartyOnly.length}`);
                    
                    // 显示上下文引用
                    try {
                        displayContextReferences(contexts);
                        contextCountSpan.textContent = contexts.length;
                        
                        // 确保上下文部分可见
                        if (contextSection && contexts.length > 0) {
                            contextSection.style.display = 'block';
                            console.log('✅ 上下文部分已显示');
                        }
                        
                        updateStatus(`找到 ${contexts.length} 个相关上下文`);
                        console.log('✅ 上下文引用显示完成');
                    } catch (error) {
                        console.error('❌ 显示上下文引用失败:', error);
                        updateStatus('显示上下文引用失败');
                    }
                    break;
            }
        }

        function updateStatus(message) {
            const statusInfo = document.getElementById('statusInfo');
            const timestamp = new Date().toLocaleTimeString();
            statusInfo.innerHTML += `<p><small class="text-muted">[${timestamp}]</small> ${message}</p>`;
            statusInfo.scrollTop = statusInfo.scrollHeight;
        }

        // 从主应用复制的函数
        function displayContextReferences(contexts) {
            console.log('🔧 displayContextReferences 开始处理:', contexts);
            
            const contextContentDiv = document.getElementById('contextContent');
            const contextSection = document.getElementById('contextSection');
            const contextCountSpan = document.getElementById('contextCount');

            // 验证DOM元素
            if (!contextContentDiv) {
                console.error('❌ contextContent 元素未找到');
                return;
            }
            if (!contextSection) {
                console.error('❌ contextSection 元素未找到');
                return;
            }
            if (!contextCountSpan) {
                console.error('❌ contextCount 元素未找到');
                return;
            }

            // 验证上下文数据
            if (!contexts || !Array.isArray(contexts)) {
                console.error('❌ 上下文数据无效:', contexts);
                contextSection.style.display = 'none';
                return;
            }

            console.log(`📊 处理 ${contexts.length} 个上下文`);

            // 分类上下文并存储到全局变量
            aiContextData.withKeywords = contexts.filter(ctx => ctx.context_type === 'with_keywords');
            aiContextData.relatedPartyOnly = contexts.filter(ctx => ctx.context_type === 'related_party_only');
            aiContextData.currentWithKeywordsPage = 1;
            aiContextData.currentRelatedPartyPage = 1;

            console.log(`📊 上下文分类结果: 包含关键词=${aiContextData.withKeywords.length}, 仅关联方=${aiContextData.relatedPartyOnly.length}`);

            // 更新上下文计数
            contextCountSpan.textContent = contexts.length;

            // 如果有上下文，显示上下文部分
            if (contexts.length > 0) {
                contextSection.style.display = 'block';
                console.log('✅ 上下文部分已设置为可见');
                
                // 简化显示内容
                let html = '<div class="alert alert-info">上下文数据已加载，共 ' + contexts.length + ' 个</div>';
                contextContentDiv.innerHTML = html;
            } else {
                contextSection.style.display = 'none';
                console.log('⚠️ 没有上下文数据，隐藏上下文部分');
                return;
            }
        }

        function debugAiContextState() {
            console.log('🔧 AI分析上下文状态调试:');
            console.log('- aiContextData:', aiContextData);
            
            const contextSection = document.getElementById('contextSection');
            const contextContent = document.getElementById('contextContent');
            const contextCount = document.getElementById('contextCount');
            
            const result = {
                aiContextData,
                elements: {
                    contextSection: contextSection ? 'found' : 'missing',
                    contextContent: contextContent ? 'found' : 'missing', 
                    contextCount: contextCount ? 'found' : 'missing'
                },
                visibility: contextSection ? contextSection.style.display : 'N/A',
                contentLength: contextContent ? contextContent.innerHTML.length : 0,
                count: contextCount ? contextCount.textContent : 'N/A'
            };
            
            console.log('调试结果:', result);
            updateStatus('调试信息已输出到控制台');
            
            return result;
        }

        // 暴露调试函数到全局作用域
        window.debugAiContextState = debugAiContextState;
    </script>
</body>
</html>
