<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误检查页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- 引入相同的库 -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.2/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-table@2.0.0/dist/markdown-it-table.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-attrs@4.1.6/dist/markdown-it-attrs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-container@3.0.0/dist/markdown-it-container.min.js"></script>
    
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="bi bi-exclamation-triangle"></i> 错误检查页面</h1>
                <p class="text-muted">检查JavaScript错误和库加载状态</p>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-check-circle"></i> 库加载状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="libraryStatus" class="row">
                            <!-- 库状态将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 功能测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2 d-md-flex">
                            <button class="btn btn-primary" onclick="testBasicFunction()">
                                <i class="bi bi-play"></i> 测试基础功能
                            </button>
                            <button class="btn btn-info" onclick="testMarkdownIt()">
                                <i class="bi bi-markdown"></i> 测试Markdown-it
                            </button>
                            <button class="btn btn-warning" onclick="testErrorHandling()">
                                <i class="bi bi-shield-exclamation"></i> 测试错误处理
                            </button>
                            <button class="btn btn-success" onclick="runAllTests()">
                                <i class="bi bi-check-all"></i> 运行所有测试
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-terminal"></i> 控制台输出</h6>
                    </div>
                    <div class="card-body">
                        <div id="consoleOutput" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                            页面加载完成，等待测试...
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-bug"></i> 错误日志</h6>
                    </div>
                    <div class="card-body">
                        <div id="errorLog" class="bg-danger bg-opacity-10 p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                            暂无错误...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-eye"></i> 测试结果</h6>
                    </div>
                    <div class="card-body">
                        <div id="testResults" class="border rounded p-3" style="min-height: 200px;">
                            测试结果将在这里显示...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 错误捕获
        window.addEventListener('error', function(e) {
            logError(`JavaScript错误: ${e.message} (${e.filename}:${e.lineno})`);
        });

        window.addEventListener('unhandledrejection', function(e) {
            logError(`Promise错误: ${e.reason}`);
        });

        // 日志函数
        function log(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function logError(message) {
            const errorLog = document.getElementById('errorLog');
            const timestamp = new Date().toLocaleTimeString();
            errorLog.innerHTML += `[${timestamp}] ❌ ${message}\n`;
            errorLog.scrollTop = errorLog.scrollHeight;
            console.error(message);
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 检查库加载状态
        function checkLibraryStatus() {
            const libraries = {
                'Bootstrap': typeof bootstrap !== 'undefined',
                'markdown-it': typeof markdownit !== 'undefined',
                'markdown-it-table': typeof markdownItTable !== 'undefined',
                'markdown-it-attrs': typeof markdownItAttrs !== 'undefined',
                'markdown-it-container': typeof markdownItContainer !== 'undefined',
                'highlight.js': typeof hljs !== 'undefined'
            };

            const statusDiv = document.getElementById('libraryStatus');
            let html = '';

            Object.entries(libraries).forEach(([name, loaded]) => {
                const status = loaded ? 'success' : 'danger';
                const icon = loaded ? 'check-circle' : 'x-circle';
                html += `
                    <div class="col-md-4 mb-2">
                        <div class="alert alert-${status} py-2">
                            <i class="bi bi-${icon}"></i> ${name}
                        </div>
                    </div>
                `;
                log(`${name}: ${loaded ? '✅' : '❌'}`);
            });

            statusDiv.innerHTML = html;
            return libraries;
        }

        // 初始化markdown-it
        let markdownRenderer = null;

        function initMarkdownRenderer() {
            if (markdownRenderer) {
                return true;
            }
            
            if (typeof markdownit === 'undefined') {
                logError('markdown-it库未加载');
                return false;
            }
            
            try {
                log('🔧 初始化markdown-it解析器...');
                
                markdownRenderer = markdownit({
                    html: true,
                    breaks: true,
                    linkify: true,
                    typographer: false,
                    quotes: '""\'\'',
                    highlight: function (str, lang) {
                        if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                            try {
                                return '<pre class="hljs"><code>' +
                                       hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                                       '</code></pre>';
                            } catch (__) {}
                        }
                        return '<pre class="hljs"><code>' + escapeHtml(str) + '</code></pre>';
                    }
                });

                if (typeof markdownItTable !== 'undefined') {
                    markdownRenderer.use(markdownItTable);
                    log('✅ 表格插件已启用');
                }

                markdownRenderer.renderer.rules.table_open = function() {
                    return '<div class="table-responsive"><table class="table table-striped table-bordered">\n';
                };

                markdownRenderer.renderer.rules.table_close = function() {
                    return '</table></div>\n';
                };

                log('✅ markdown-it解析器初始化完成');
                return true;
                
            } catch (e) {
                logError('markdown-it初始化失败: ' + e.message);
                return false;
            }
        }

        // 测试基础功能
        function testBasicFunction() {
            log('🧪 开始测试基础功能...');
            
            try {
                // 测试数组操作
                const testArray = [1, 2, 3, 4, 5];
                const result = testArray.map(x => x * 2);
                log('✅ 数组操作正常: ' + result.join(', '));
                
                // 测试DOM操作
                const testDiv = document.createElement('div');
                testDiv.textContent = '测试内容';
                log('✅ DOM操作正常');
                
                // 测试事件处理
                testDiv.addEventListener('click', function() {
                    log('✅ 事件处理正常');
                });
                
                log('✅ 基础功能测试完成');
                return true;
                
            } catch (e) {
                logError('基础功能测试失败: ' + e.message);
                return false;
            }
        }

        // 测试markdown-it
        function testMarkdownIt() {
            log('🧪 开始测试markdown-it...');
            
            if (!initMarkdownRenderer()) {
                logError('markdown-it初始化失败');
                return false;
            }

            const testContent = `# 测试标题

这是一个**粗体**文本和*斜体*文本的测试。

| 列1 | 列2 | 列3 |
|---|---|---|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

\`\`\`javascript
function test() {
    console.log("Hello World");
}
\`\`\`

- 列表项1
- 列表项2
- 列表项3`;

            try {
                const result = markdownRenderer.render(testContent);
                log('✅ markdown-it解析成功');
                log('原始内容长度: ' + testContent.length);
                log('解析结果长度: ' + result.length);
                
                document.getElementById('testResults').innerHTML = result;
                return true;
                
            } catch (e) {
                logError('markdown-it解析失败: ' + e.message);
                return false;
            }
        }

        // 测试错误处理
        function testErrorHandling() {
            log('🧪 开始测试错误处理...');
            
            try {
                // 测试空内容
                const result1 = markdownRenderer ? markdownRenderer.render('') : '';
                log('✅ 空内容处理正常');
                
                // 测试特殊字符
                const result2 = markdownRenderer ? markdownRenderer.render('# 测试\n\n包含<script>alert("test")</script>的内容') : '';
                log('✅ 特殊字符处理正常');
                
                // 测试错误的markdown
                const result3 = markdownRenderer ? markdownRenderer.render('```javascript\nfunction test() {\n    console.log("unclosed') : '';
                log('✅ 错误markdown处理正常');
                
                return true;
                
            } catch (e) {
                logError('错误处理测试失败: ' + e.message);
                return false;
            }
        }

        // 运行所有测试
        function runAllTests() {
            log('🚀 开始运行所有测试...');
            
            const results = {
                libraries: checkLibraryStatus(),
                basicFunction: testBasicFunction(),
                markdownIt: testMarkdownIt(),
                errorHandling: testErrorHandling()
            };
            
            const allPassed = Object.values(results).every(result => 
                typeof result === 'boolean' ? result : Object.values(result).every(Boolean)
            );
            
            if (allPassed) {
                log('🎉 所有测试通过！');
                document.getElementById('testResults').innerHTML = `
                    <div class="alert alert-success">
                        <h5><i class="bi bi-check-circle"></i> 所有测试通过！</h5>
                        <p>所有功能都正常工作，可以安全使用。</p>
                    </div>
                `;
            } else {
                logError('部分测试失败，请查看详细日志');
                document.getElementById('testResults').innerHTML = `
                    <div class="alert alert-warning">
                        <h5><i class="bi bi-exclamation-triangle"></i> 部分测试失败</h5>
                        <p>请查看错误日志了解详情。</p>
                    </div>
                `;
            }
        }

        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成');
            
            setTimeout(() => {
                log('⏰ 开始自动检查...');
                checkLibraryStatus();
                
                setTimeout(() => {
                    if (typeof markdownit !== 'undefined') {
                        initMarkdownRenderer();
                    } else {
                        logError('markdown-it库未加载，请检查网络连接');
                    }
                }, 500);
            }, 100);
        });
    </script>
</body>
</html>
