#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Notion 备份工具 - 混合 API 版本
使用官方 Integration API 进行验证和基本操作
使用内部 API 进行导出功能（因为官方 API 不支持导出）
"""

import os
import shutil
import time
import json
import zipfile
import requests
import argparse
import subprocess
import re
from datetime import datetime
from notify import send

try:
    from notion_client import Client
    NOTION_CLIENT_AVAILABLE = True
except ImportError:
    NOTION_CLIENT_AVAILABLE = False
    print("Warning: notion-client not installed. Using legacy mode only.")

# 配置
DEFAULT_BACKUP_CONFIG = {
    "spaces": []
}

# 是否去除所有文件和文件夹的id
REMOVE_FILES_ID = False

# API 配置
NOTION_TIMEZONE = os.getenv("NOTION_TIMEZONE", "Asia/Shanghai")
NOTION_LOCALE = os.getenv("NOTION_LOCALE", "en")
NOTION_API_V3 = os.getenv("NOTION_API", "https://www.notion.so/api/v3")  # 内部 API
NOTION_API_V1 = "https://api.notion.com/v1"  # 官方 API

# 认证信息
NOTION_TOKEN = os.getenv("NOTION_TOKEN", "")  # 浏览器 token_v2 (用于导出)
NOTION_INTEGRATION_TOKEN = os.getenv("NOTION_INTEGRATION_TOKEN", "")  # 官方 Integration Token
NOTION_EMAIL = os.getenv("NOTION_EMAIL", "")
NOTION_PASSWORD = os.getenv("NOTION_PASSWORD", "")

# 其他配置
NOTION_FILE_TOKEN = ""
NOTION_EXPORT_TYPE = os.getenv("NOTION_EXPORT_TYPE", "markdown")
SAVE_DIR = "backup"

# Git 配置
REPOSITORY_URL = "https://github.com/8ybing/notion-backup.git"
REPOSITORY_BRANCH = "master"
GIT_USERNAME = "git_user_name"
GIT_EMAIL = "***********"


def run_command(cmd):
    """执行命令行命令"""
    try:
        proc = subprocess.run(
            cmd,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            encoding="utf-8",
        )
        if proc.stderr != "":
            print("cmd:{} stdout:{} stderr:{}".format(cmd, proc.stdout, proc.stderr))
        return proc.stderr == "", proc.stdout, proc.stderr
    except Exception as e:
        return False, "", str(e)


def writeLog(s):
    """写入日志并发送通知"""
    with open("log.txt", "a") as log:
        msg = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()) + " " + s
        print(msg)
        send("notion备份", msg)
        log.write(msg + "\n")


def unzip(filename: str, saveDir: str = ""):
    """解压文件"""
    try:
        file = zipfile.ZipFile(filename)
        dirname = filename.replace(".zip", "")
        if saveDir != "":
            dirname = saveDir
        if os.path.exists(dirname):
            print(f"{dirname} 已存在,将被覆盖")
            shutil.rmtree(dirname)
        os.mkdir(dirname)
        file.extractall(dirname)
        file.close()
        return dirname
    except Exception as e:
        print(f"{filename} unzip fail,{str(e)}")


def zip_dir(dirpath, outFullName):
    """压缩指定文件夹"""
    zip = zipfile.ZipFile(outFullName, "w", zipfile.ZIP_DEFLATED)
    for path, dirnames, filenames in os.walk(dirpath):
        fpath = path.replace(dirpath, "")
        for filename in filenames:
            zip.write(os.path.join(path, filename), os.path.join(fpath, filename))
    zip.close()


class NotionBackup:
    """Notion 备份类 - 混合 API 实现"""
    
    def __init__(self):
        self.notion_client = None
        self.v2_token = NOTION_TOKEN
        self.integration_token = NOTION_INTEGRATION_TOKEN
        self.file_token = ""
        
        # 初始化官方客户端
        if NOTION_CLIENT_AVAILABLE and self.integration_token:
            try:
                self.notion_client = Client(auth=self.integration_token)
                print("✓ 官方 Integration API 初始化成功")
            except Exception as e:
                print(f"✗ 官方 Integration API 初始化失败: {e}")
                self.notion_client = None
        
        # 验证 v2 token
        if not self.v2_token or self.v2_token == "YOUR TOKEN":
            print("✗ 警告: NOTION_TOKEN (token_v2) 未设置，导出功能将不可用")
    
    def verify_integration_token(self):
        """验证官方 Integration Token"""
        if not self.notion_client:
            return False
        
        try:
            # 尝试获取用户信息
            response = self.notion_client.users.me()
            print(f"✓ Integration Token 验证成功，用户: {response.get('name', 'Unknown')}")
            return True
        except Exception as e:
            print(f"✗ Integration Token 验证失败: {e}")
            return False
    
    def verify_v2_token(self):
        """验证 v2 token"""
        if not self.v2_token:
            return False
        
        try:
            result = self._request_v3_api("loadUserContent", {})
            if result and "recordMap" in result:
                print("✓ V2 Token 验证成功")
                return True
            else:
                print("✗ V2 Token 验证失败")
                return False
        except Exception as e:
            print(f"✗ V2 Token 验证失败: {e}")
            return False
    
    def _request_v3_api(self, endpoint: str, params: dict, max_retries=3, retry_time_seconds=10):
        """调用 Notion 内部 API (v3)"""
        print(f"调用内部 API: {endpoint}")
        
        attempt = 0
        while attempt < max_retries:
            try:
                response = requests.post(
                    f"{NOTION_API_V3}/{endpoint}",
                    data=json.dumps(params).encode("utf8"),
                    headers={
                        "content-type": "application/json",
                        "cookie": f"token_v2={self.v2_token};",
                    },
                    timeout=60,
                )
                
                if response.status_code == 401:
                    raise Exception(f"Token 认证失败 (401): token_v2 可能已过期")
                if response.status_code in [504, 500, 503]:
                    raise Exception(f"服务器错误 ({response.status_code}): 尝试重新连接")
                
                if response.status_code == 200:
                    # 获取 file_token
                    file_token = response.cookies.get("file_token")
                    if not self.file_token and file_token:
                        self.file_token = file_token
                    return response.json()
                else:
                    print(f"API 调用失败: {endpoint}, 状态码: {response.status_code}")
                    
            except Exception as e:
                print(f"API 调用异常: {endpoint}, 错误: {e}")
                attempt += 1
                if attempt < max_retries:
                    time.sleep(retry_time_seconds)
        
        print(f"API 调用最终失败: {endpoint}, 重试 {max_retries} 次后放弃")
        return None

    def get_user_content(self):
        """获取用户内容（使用内部 API）"""
        result = self._request_v3_api("loadUserContent", {})
        if result is None:
            writeLog("获取用户内容失败: Token 可能已过期")
            raise Exception("无法获取用户内容，请检查 NOTION_TOKEN")
        return result.get("recordMap", {})

    def create_export_task(self, space_id, block_id=None):
        """创建导出任务"""
        if block_id:
            # 导出特定页面/块
            task_data = {
                "task": {
                    "eventName": "exportBlock",
                    "request": {
                        "block": {"id": block_id, "spaceId": space_id},
                        "recursive": True,
                        "exportOptions": {
                            "exportType": NOTION_EXPORT_TYPE,
                            "timeZone": NOTION_TIMEZONE,
                            "locale": NOTION_LOCALE,
                            "flattenExportFiletree": False,
                        },
                    },
                }
            }
        else:
            # 导出整个空间
            task_data = {
                "task": {
                    "eventName": "exportSpace",
                    "request": {
                        "spaceId": space_id,
                        "exportOptions": {
                            "exportType": NOTION_EXPORT_TYPE,
                            "timeZone": NOTION_TIMEZONE,
                            "locale": NOTION_LOCALE,
                            "flattenExportFiletree": False,
                        },
                    },
                }
            }

        result = self._request_v3_api("enqueueTask", task_data)
        if result:
            return result.get("taskId")
        return None

    def wait_for_export(self, task_id):
        """等待导出任务完成并获取下载链接"""
        print(f"等待导出任务完成: {task_id}")

        while True:
            result = self._request_v3_api(
                "getTasks",
                {"taskIds": [task_id]},
                max_retries=5,
                retry_time_seconds=15
            )

            if not result or "results" not in result:
                print("获取任务状态失败")
                return None

            tasks = result["results"]
            task = next((t for t in tasks if t["id"] == task_id), None)

            if not task:
                print("找不到对应的任务")
                return None

            if task["state"] == "success":
                url = task["status"]["exportURL"]
                print(f"导出成功，下载链接: {url}")
                return url
            elif task["state"] == "failure":
                error = task.get("error", "未知错误")
                print(f"导出失败: {error}")
                writeLog(f"导出任务失败: {error}")
                return None
            else:
                print(f"任务状态: {task['state']}", end="", flush=True)
                time.sleep(20)

    def download_and_extract(self, url, filename):
        """下载并解压导出文件"""
        os.makedirs(SAVE_DIR, exist_ok=True)
        save_path = os.path.join(SAVE_DIR, filename)

        print(f"开始下载: {filename}")

        try:
            headers = {}
            if self.file_token:
                headers["cookie"] = f"file_token={self.file_token}"

            with requests.get(url, stream=True, headers=headers) as r:
                r.raise_for_status()
                with open(save_path, "wb") as f:
                    shutil.copyfileobj(r.raw, f)

            print(f"下载完成: {save_path}")

            # 解压文件
            extract_dir = unzip(save_path)

            # 处理嵌套的 zip 文件
            if extract_dir:
                for file in os.listdir(extract_dir):
                    file_path = os.path.join(extract_dir, file)
                    if file.endswith(".zip"):
                        unzip(file_path)
                        os.remove(file_path)

            # 可选：移除文件 ID
            if REMOVE_FILES_ID:
                self._remove_files_id()
                os.remove(save_path)
                zip_dir(extract_dir, save_path)

            return True

        except Exception as e:
            print(f"下载失败: {e}")
            writeLog(f"下载文件失败: {filename}, 错误: {e}")
            return False

    def _remove_files_id(self):
        """移除文件和文件夹中的 ID"""
        if not REMOVE_FILES_ID:
            return

        # 移除文件名中的 ID
        for root, dirs, files in os.walk(SAVE_DIR):
            for file in files:
                path = os.path.join(root, file)
                filename_id = re.compile(r"[a-fA-F\d]{32}").findall(file)
                if filename_id:
                    new_filename = file.replace(" " + filename_id[0], "")
                    new_path = os.path.join(root, new_filename)
                    os.rename(path, new_path)

        # 移除文件夹名中的 ID
        while True:
            rename_dir_flag = False
            for root, dirs, files in os.walk(SAVE_DIR):
                for dir in dirs:
                    path = os.path.join(root, dir)
                    dir_id = re.compile(
                        r"[a-fA-F\d]{8}-[a-fA-F\d]{4}-[a-fA-F\d]{4}-[a-fA-F\d]{4}-[a-fA-F\d]{12}"
                    ).findall(dir)
                    if dir_id:
                        new_dirname = dir.replace("-" + dir_id[0], "")
                        new_path = os.path.join(root, new_dirname)
                        os.rename(path, new_path)
                        rename_dir_flag = True
                        break
            if not rename_dir_flag:
                break
