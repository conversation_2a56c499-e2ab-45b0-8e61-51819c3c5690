<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特定问题修复测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <style>
        .comparison-container {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        .before-after {
            display: flex;
            height: 400px;
        }
        .before, .after {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            font-size: 0.9rem;
        }
        .before {
            background-color: #fff5f5;
            border-right: 1px solid #dee2e6;
        }
        .after {
            background-color: #f0fff4;
        }
        .issue-tag {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            margin: 0.25rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }
        .issue-table { background-color: #ffeaa7; color: #2d3436; }
        .issue-bold { background-color: #fab1a0; color: #2d3436; }
        .issue-paragraph { background-color: #fd79a8; color: #2d3436; }
        .fixed { background-color: #00b894; color: white; }
        
        .markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }
        .markdown-content p {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }
        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 0.75rem 0;
        }
        .markdown-content th, .markdown-content td {
            border: 1px solid #dee2e6;
            padding: 0.5rem;
            text-align: left;
        }
        .markdown-content th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="bi bi-tools"></i> 特定问题修复测试</h1>
                <p class="text-muted">测试针对诊断出的具体问题的修复效果</p>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-bug"></i> 问题1: 表格缺少分隔线</h5>
                        <div class="mt-2">
                            <span class="issue-tag issue-table">表格格式问题</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="comparison-container">
                            <div class="row g-0">
                                <div class="col-6 bg-danger bg-opacity-10">
                                    <div class="p-3 border-end">
                                        <h6><i class="bi bi-x-circle text-danger"></i> 修复前</h6>
                                        <div id="table-before" class="border rounded p-2 bg-white">
                                            <!-- 原始问题内容 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 bg-success bg-opacity-10">
                                    <div class="p-3">
                                        <h6><i class="bi bi-check-circle text-success"></i> 修复后</h6>
                                        <div id="table-after" class="border rounded p-2 bg-white markdown-content">
                                            <!-- 修复后内容 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-bug"></i> 问题2: 过多粗体文本影响标题识别</h5>
                        <div class="mt-2">
                            <span class="issue-tag issue-bold">粗体文本问题</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="comparison-container">
                            <div class="row g-0">
                                <div class="col-6 bg-danger bg-opacity-10">
                                    <div class="p-3 border-end">
                                        <h6><i class="bi bi-x-circle text-danger"></i> 修复前</h6>
                                        <div id="bold-before" class="border rounded p-2 bg-white">
                                            <!-- 原始问题内容 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 bg-success bg-opacity-10">
                                    <div class="p-3">
                                        <h6><i class="bi bi-check-circle text-success"></i> 修复后</h6>
                                        <div id="bold-after" class="border rounded p-2 bg-white markdown-content">
                                            <!-- 修复后内容 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-bug"></i> 问题3: 过长段落影响可读性</h5>
                        <div class="mt-2">
                            <span class="issue-tag issue-paragraph">段落分割问题</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="comparison-container">
                            <div class="row g-0">
                                <div class="col-6 bg-danger bg-opacity-10">
                                    <div class="p-3 border-end">
                                        <h6><i class="bi bi-x-circle text-danger"></i> 修复前</h6>
                                        <div id="paragraph-before" class="border rounded p-2 bg-white">
                                            <!-- 原始问题内容 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 bg-success bg-opacity-10">
                                    <div class="p-3">
                                        <h6><i class="bi bi-check-circle text-success"></i> 修复后</h6>
                                        <div id="paragraph-after" class="border rounded p-2 bg-white markdown-content">
                                            <!-- 修复后内容 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-check-all"></i> 综合测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <button class="btn btn-primary" onclick="runAllTests()">
                                <i class="bi bi-play"></i> 运行所有测试
                            </button>
                            <button class="btn btn-outline-info" onclick="showTestData()">
                                <i class="bi bi-eye"></i> 查看测试数据
                            </button>
                            <button class="btn btn-outline-success" onclick="exportResults()">
                                <i class="bi bi-download"></i> 导出结果
                            </button>
                        </div>
                        
                        <div id="test-results" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> 测试结果</h6>
                                <div id="results-content"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 配置marked.js
        marked.setOptions({
            breaks: true,
            gfm: true,
            sanitize: false,
            smartLists: true,
            smartypants: false,
            xhtml: false,
            pedantic: false,
            silent: false
        });

        // 测试数据
        const testData = {
            tableIssue: {
                before: `**年报主体公司/代码** | **是否存在协同创新** | **原文数据**
测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作
子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同
子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目`,
                
                after: `| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |
|---|---|---|
| 测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作 |
| 子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同 |
| 子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目 |`
            },
            
            boldIssue: {
                before: `**分析报告：** **测试公司协同创新情况**

**1. 基本情况：** **公司名称：** **测试公司** **股票代码：** **000000**

**2. 协同创新：** **技术合作：** **是** **资源共享：** **是** **市场协同：** **是**`,
                
                after: `# 分析报告：测试公司协同创新情况

## 1. 基本情况

#### 公司名称
测试公司

#### 股票代码
000000

## 2. 协同创新

#### 技术合作
**是**

#### 资源共享
**是**

#### 市场协同
**是**`
            },
            
            paragraphIssue: {
                before: `该公司在关联方协同创新方面表现积极，通过技术合作和资源整合，有效提升了整体创新能力。公司与多家关联方建立了长期稳定的合作关系，在研发、生产、销售等多个环节实现了深度协同。特别是在技术研发领域，公司与关联方共同投入资源，建立联合研发中心，开展前沿技术攻关，取得了显著成效。同时，公司还通过资源共享机制，与关联方实现了设备、人才、信息等资源的优化配置，提高了整体运营效率。在市场拓展方面，公司与关联方形成了良好的协同效应，通过统一的市场策略和品牌推广，扩大了市场影响力，提升了竞争优势。`,
                
                after: `该公司在关联方协同创新方面表现积极，通过技术合作和资源整合，有效提升了整体创新能力。公司与多家关联方建立了长期稳定的合作关系，在研发、生产、销售等多个环节实现了深度协同。

特别是在技术研发领域，公司与关联方共同投入资源，建立联合研发中心，开展前沿技术攻关，取得了显著成效。同时，公司还通过资源共享机制，与关联方实现了设备、人才、信息等资源的优化配置，提高了整体运营效率。

在市场拓展方面，公司与关联方形成了良好的协同效应，通过统一的市场策略和品牌推广，扩大了市场影响力，提升了竞争优势。`
            }
        };

        // 从主应用复制的修复函数
        function formatAIContentSimple(content) {
            if (!content) return '';

            try {
                let formatted = content;

                formatted = formatted.replace(/\*\*([^*]*分析报告[^*]*)\*\*/g, '\n# $1\n');
                formatted = formatted.replace(/\*\*([^*]{3,50})[：:]\s*\*\*/g, '\n## $1\n');
                formatted = formatted.replace(/^(\d+\.\s*[^：\n]{3,50})[：:]\s*/gm, '\n### $1\n\n');
                formatted = formatted.replace(/\*\*([^*]{2,15})[：:]\*\*/g, (match, text) => {
                    if (text.length <= 15 && !text.includes('|')) {
                        return `\n#### ${text}\n`;
                    }
                    return match;
                });

                formatted = splitLongParagraphs(formatted);
                formatted = formatted.replace(/\*\*\s*\*\*/g, '');
                formatted = formatted.replace(/\*\*([^*]{1,30})\*\*/g, (match, text) => {
                    if (text.length <= 10 || text.match(/^(是|否|技术|合作|创新|协同)$/)) {
                        return `**${text}**`;
                    }
                    return text;
                });

                formatted = formatted.replace(/\n{4,}/g, '\n\n\n');
                formatted = formatted.replace(/\n{3}/g, '\n\n');

                return formatted.trim();
            } catch (e) {
                console.warn('AI内容格式化失败:', e);
                return content;
            }
        }

        function fixTableFormatSimple(content) {
            if (!content) return '';

            try {
                let fixed = content;

                fixed = fixed.replace(/\*\*年报主体公司[\/\\]代码\*\*\s*\|\s*\*\*是否存在协同创新\*\*\s*\|\s*\*\*原文数据\*\*/g,
                    '\n| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |\n|---|---|---|');

                const lines = fixed.split('\n');
                const processedLines = [];
                let inTable = false;
                let tableHeaderFound = false;

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i].trim();
                    const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';

                    if (line.includes('|') && line.split('|').length >= 3) {
                        if (!inTable) {
                            inTable = true;
                            tableHeaderFound = false;
                        }

                        if (!tableHeaderFound && (line.includes('年报主体公司') || line.includes('是否存在') ||
                            (line.match(/\|[^|]*\|[^|]*\|/) && !nextLine.match(/^\|[\s\-:=|]+\|$/)))) {
                            processedLines.push(line);

                            const columnCount = line.split('|').filter(cell => cell.trim() !== '').length;
                            const separator = '|' + '---|'.repeat(columnCount);
                            processedLines.push(separator);
                            tableHeaderFound = true;
                            continue;
                        }

                        processedLines.push(line);
                    } else {
                        if (inTable) {
                            inTable = false;
                            tableHeaderFound = false;
                        }
                        processedLines.push(line);
                    }
                }

                return processedLines.join('\n');
            } catch (e) {
                console.warn('表格格式修复失败:', e);
                return content;
            }
        }

        function splitLongParagraphs(content) {
            try {
                const paragraphs = content.split('\n\n');
                const processedParagraphs = [];

                for (const paragraph of paragraphs) {
                    if (paragraph.length > 300 && !paragraph.includes('|') && !paragraph.startsWith('#')) {
                        const sentences = paragraph.split(/([。！？])/);
                        let currentParagraph = '';

                        for (let i = 0; i < sentences.length; i += 2) {
                            const sentence = sentences[i] + (sentences[i + 1] || '');

                            if (currentParagraph.length + sentence.length > 200 && currentParagraph.length > 0) {
                                processedParagraphs.push(currentParagraph.trim());
                                currentParagraph = sentence;
                            } else {
                                currentParagraph += sentence;
                            }
                        }

                        if (currentParagraph.trim()) {
                            processedParagraphs.push(currentParagraph.trim());
                        }
                    } else {
                        processedParagraphs.push(paragraph);
                    }
                }

                return processedParagraphs.join('\n\n');
            } catch (e) {
                console.warn('段落分割失败:', e);
                return content;
            }
        }

        // 测试函数
        function runAllTests() {
            console.log('🚀 开始运行所有测试...');

            // 测试表格问题修复
            testTableFix();

            // 测试粗体文本问题修复
            testBoldFix();

            // 测试段落分割问题修复
            testParagraphFix();

            // 显示结果
            showTestResults();
        }

        function testTableFix() {
            const before = testData.tableIssue.before;
            const fixed = fixTableFormatSimple(before);

            document.getElementById('table-before').innerHTML = '<pre>' + before + '</pre>';
            document.getElementById('table-after').innerHTML = marked.parse(fixed);

            console.log('✅ 表格修复测试完成');
        }

        function testBoldFix() {
            const before = testData.boldIssue.before;
            const fixed = formatAIContentSimple(before);

            document.getElementById('bold-before').innerHTML = '<pre>' + before + '</pre>';
            document.getElementById('bold-after').innerHTML = marked.parse(fixed);

            console.log('✅ 粗体文本修复测试完成');
        }

        function testParagraphFix() {
            const before = testData.paragraphIssue.before;
            const fixed = splitLongParagraphs(before);

            document.getElementById('paragraph-before').innerHTML = '<pre>' + before + '</pre>';
            document.getElementById('paragraph-after').innerHTML = marked.parse(fixed);

            console.log('✅ 段落分割修复测试完成');
        }

        function showTestResults() {
            const resultsDiv = document.getElementById('test-results');
            const contentDiv = document.getElementById('results-content');

            contentDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-4">
                        <span class="issue-tag fixed">表格分隔线</span>
                        <p class="small mb-0">已自动添加缺失的表格分隔线</p>
                    </div>
                    <div class="col-md-4">
                        <span class="issue-tag fixed">标题转换</span>
                        <p class="small mb-0">粗体文本已转换为合适的标题</p>
                    </div>
                    <div class="col-md-4">
                        <span class="issue-tag fixed">段落分割</span>
                        <p class="small mb-0">长段落已智能分割为多个段落</p>
                    </div>
                </div>
            `;

            resultsDiv.style.display = 'block';
        }

        function showTestData() {
            console.group('📋 测试数据');
            console.log('表格问题数据:', testData.tableIssue);
            console.log('粗体文本问题数据:', testData.boldIssue);
            console.log('段落问题数据:', testData.paragraphIssue);
            console.groupEnd();
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                tests: {
                    tableIssue: {
                        before: testData.tableIssue.before,
                        after: fixTableFormatSimple(testData.tableIssue.before),
                        status: 'fixed'
                    },
                    boldIssue: {
                        before: testData.boldIssue.before,
                        after: formatAIContentSimple(testData.boldIssue.before),
                        status: 'fixed'
                    },
                    paragraphIssue: {
                        before: testData.paragraphIssue.before,
                        after: splitLongParagraphs(testData.paragraphIssue.before),
                        status: 'fixed'
                    }
                }
            };

            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'markdown_fix_test_results.json';
            a.click();
            URL.revokeObjectURL(url);
        }

        // 页面加载时自动运行测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
