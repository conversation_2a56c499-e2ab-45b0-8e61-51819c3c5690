<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown-it 功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Markdown-it 库 -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.2/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-table@2.0.0/dist/markdown-it-table.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-attrs@4.1.6/dist/markdown-it-attrs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-container@3.0.0/dist/markdown-it-container.min.js"></script>
    
    <!-- 代码高亮 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/highlight.min.js"></script>
    
    <style>
        .comparison-panel {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .source-panel {
            background-color: #f8f9fa;
        }
        
        .result-panel {
            background-color: #fff;
        }
        
        /* 复制代码块样式 */
        .code-block-wrapper {
            position: relative;
            margin: 1rem 0;
        }
        
        .copy-code-btn {
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }
        
        .code-block-wrapper:hover .copy-code-btn {
            opacity: 1;
        }
        
        /* 表格样式 */
        .table-responsive::after {
            content: "← 可左右滚动 →";
            position: absolute;
            bottom: -1.5rem;
            right: 0;
            font-size: 0.75rem;
            color: #6c757d;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .table-responsive:hover::after {
            opacity: 1;
        }
        
        /* 容器样式 */
        .container-warning {
            border-left: 4px solid #ffc107;
            background-color: #fff3cd;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 6px 6px 0;
        }
        
        .container-info {
            border-left: 4px solid #0dcaf0;
            background-color: #d1ecf1;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 6px 6px 0;
        }
        
        .container-success {
            border-left: 4px solid #198754;
            background-color: #d1e7dd;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 6px 6px 0;
        }
        
        .container-danger {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0 6px 6px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="bi bi-markdown"></i> Markdown-it 功能测试</h1>
                <p class="text-muted">测试新的markdown-it渲染器的功能和性能</p>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 测试控制</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">选择测试内容:</label>
                                <select id="testContent" class="form-select mb-3">
                                    <option value="basic">基础语法测试</option>
                                    <option value="table">表格测试</option>
                                    <option value="code">代码高亮测试</option>
                                    <option value="ai_report">AI分析报告测试</option>
                                    <option value="complex">复杂内容测试</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">操作:</label>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="runTest()">
                                        <i class="bi bi-play"></i> 运行测试
                                    </button>
                                    <button class="btn btn-outline-info" onclick="checkStatus()">
                                        <i class="bi bi-info-circle"></i> 检查状态
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-file-text"></i> 源码</h6>
                    </div>
                    <div class="card-body p-0">
                        <div id="sourcePanel" class="comparison-panel source-panel">
                            <p class="text-muted">请选择测试内容并点击运行测试</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-eye"></i> 渲染结果</h6>
                    </div>
                    <div class="card-body p-0">
                        <div id="resultPanel" class="comparison-panel result-panel">
                            <p class="text-muted">渲染结果将在这里显示</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-speedometer2"></i> 性能统计</h6>
                    </div>
                    <div class="card-body">
                        <div id="performanceStats" class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="parseTime" class="text-primary">-</h4>
                                    <small class="text-muted">解析时间 (ms)</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="sourceLength" class="text-info">-</h4>
                                    <small class="text-muted">源码长度</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="resultLength" class="text-success">-</h4>
                                    <small class="text-muted">结果长度</small>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h4 id="compressionRatio" class="text-warning">-</h4>
                                    <small class="text-muted">压缩比</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-terminal"></i> 控制台输出</h6>
                    </div>
                    <div class="card-body">
                        <div id="consoleOutput" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                            等待测试开始...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 测试数据
        const testData = {
            basic: `# 基础语法测试

这是一个**粗体**文本和*斜体*文本的测试。

## 列表测试

- 无序列表项1
- 无序列表项2
  - 嵌套项1
  - 嵌套项2

1. 有序列表项1
2. 有序列表项2

## 链接和图片

[这是一个链接](https://example.com)

## 引用

> 这是一个引用块
> 可以包含多行内容`,

            table: `# 表格测试

## 基础表格

| 列1 | 列2 | 列3 |
|---|---|---|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 复杂表格

| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |
|---|---|---|
| 测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作，共同推进产品创新。 |
| 子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同，提升整体竞争力。 |
| 子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目。 |`,

            code: `# 代码高亮测试

## JavaScript代码

\`\`\`javascript
function safeMarkdownParse(content) {
    try {
        const result = markdownRenderer.render(content);
        return result;
    } catch (error) {
        console.error('解析失败:', error);
        return content;
    }
}
\`\`\`

## Python代码

\`\`\`python
def analyze_data(data):
    """分析数据函数"""
    result = []
    for item in data:
        if item.get('innovation'):
            result.append(item)
    return result
\`\`\`

## 行内代码

这是一个 \`inline code\` 示例。`,

            ai_report: `# AI分析报告：测试公司协同创新情况

## 1. 基本信息

**公司名称**: 测试公司  
**股票代码**: 000000  
**分析时间**: 2024年

## 2. 详细分析表

| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |
|---|---|---|
| 测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作，共同推进产品创新。 |
| 子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同，提升整体竞争力。 |
| 子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目。 |

## 3. 协同创新具体表现

### 3.1 技术研发合作
- **联合研发中心**: 公司与关联方建立联合研发中心
- **技术共享**: 共享核心技术和专利资源
- **人才交流**: 定期进行技术人员交流

### 3.2 资源共享
- **设备共享**: 共享研发设备和实验室
- **数据共享**: 建立统一的数据平台
- **供应链协同**: 优化供应链管理

## 4. 总结

该公司在关联方协同创新方面表现积极，通过技术合作和资源整合，有效提升了整体创新能力。`,

            complex: `# 复杂内容测试

## 混合内容

这是一个包含**多种元素**的复杂测试。

### 代码和表格混合

\`\`\`javascript
// 数据处理函数
function processData(data) {
    return data.map(item => ({
        ...item,
        processed: true
    }));
}
\`\`\`

处理结果如下表所示：

| ID | 名称 | 状态 | 备注 |
|---|---|---|---|
| 1 | 项目A | 完成 | 测试通过 |
| 2 | 项目B | 进行中 | 需要优化 |
| 3 | 项目C | 待开始 | 等待资源 |

### 嵌套列表和引用

1. **第一级项目**
   - 子项目1
     - 详细任务A
     - 详细任务B
   - 子项目2
     > 这是一个重要的注意事项
     > 需要特别关注

2. **第二级项目**
   - 另一个子项目

### 特殊字符测试

包含特殊字符：`<script>alert('test')</script>`

以及一些Unicode字符：🚀 ✅ ❌ 📊`
        };

        // 全局变量
        let markdownRenderer = null;

        // 初始化markdown-it
        function initMarkdownIt() {
            try {
                log('🔧 初始化markdown-it解析器...');

                markdownRenderer = markdownit({
                    html: true,
                    breaks: true,
                    linkify: true,
                    typographer: false,
                    quotes: '""''',
                    highlight: function (str, lang) {
                        if (lang && hljs.getLanguage(lang)) {
                            try {
                                return '<pre class="hljs"><code>' +
                                       hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                                       '</code></pre>';
                            } catch (__) {}
                        }
                        return '<pre class="hljs"><code>' + markdownRenderer.utils.escapeHtml(str) + '</code></pre>';
                    }
                });

                // 启用插件
                if (typeof markdownItTable !== 'undefined') {
                    markdownRenderer.use(markdownItTable);
                    log('✅ 表格插件已启用');
                }

                if (typeof markdownItAttrs !== 'undefined') {
                    markdownRenderer.use(markdownItAttrs);
                    log('✅ 属性插件已启用');
                }

                if (typeof markdownItContainer !== 'undefined') {
                    markdownRenderer.use(markdownItContainer, 'warning')
                                   .use(markdownItContainer, 'info')
                                   .use(markdownItContainer, 'success')
                                   .use(markdownItContainer, 'danger');
                    log('✅ 容器插件已启用');
                }

                // 自定义表格渲染
                markdownRenderer.renderer.rules.table_open = function() {
                    return '<div class="table-responsive"><table class="table table-striped table-bordered">\n';
                };

                markdownRenderer.renderer.rules.table_close = function() {
                    return '</table></div>\n';
                };

                log('✅ markdown-it解析器初始化完成');
                return true;
            } catch (e) {
                log('❌ markdown-it初始化失败: ' + e.message);
                return false;
            }
        }

        // 日志函数
        function log(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        // 检查状态
        function checkStatus() {
            log('📋 检查markdown-it状态...');

            const status = {
                'markdown-it': typeof markdownit !== 'undefined',
                'markdown-it-table': typeof markdownItTable !== 'undefined',
                'markdown-it-attrs': typeof markdownItAttrs !== 'undefined',
                'markdown-it-container': typeof markdownItContainer !== 'undefined',
                'highlight.js': typeof hljs !== 'undefined',
                'renderer': markdownRenderer !== null
            };

            Object.entries(status).forEach(([key, value]) => {
                log(`${key}: ${value ? '✅' : '❌'}`);
            });

            if (!markdownRenderer) {
                log('⚠️ 解析器未初始化，尝试重新初始化...');
                initMarkdownIt();
            }
        }

        // 运行测试
        function runTest() {
            const contentType = document.getElementById('testContent').value;
            const sourceContent = testData[contentType];

            if (!sourceContent) {
                log('❌ 未找到测试内容');
                return;
            }

            if (!markdownRenderer) {
                log('❌ markdown-it解析器未初始化');
                return;
            }

            log(`🧪 开始测试: ${contentType}`);

            // 显示源码
            document.getElementById('sourcePanel').innerHTML = '<pre>' + escapeHtml(sourceContent) + '</pre>';

            // 性能测试
            const startTime = performance.now();

            try {
                // 解析markdown
                const result = markdownRenderer.render(sourceContent);

                const endTime = performance.now();
                const parseTime = (endTime - startTime).toFixed(2);

                // 后处理
                const processedResult = postProcessHTML(result);

                // 显示结果
                document.getElementById('resultPanel').innerHTML = processedResult;

                // 更新统计
                updateStats(parseTime, sourceContent.length, result.length);

                log(`✅ 测试完成，耗时: ${parseTime}ms`);

            } catch (e) {
                log(`❌ 测试失败: ${e.message}`);
                document.getElementById('resultPanel').innerHTML =
                    '<div class="alert alert-danger">解析失败: ' + escapeHtml(e.message) + '</div>';
            }
        }

        // 后处理HTML
        function postProcessHTML(html) {
            try {
                // 添加代码复制按钮
                html = html.replace(/<pre class="hljs"><code>/g,
                    '<div class="code-block-wrapper"><pre class="hljs"><code>');
                html = html.replace(/<\/code><\/pre>/g,
                    '</code></pre><button class="btn btn-sm btn-outline-secondary copy-code-btn" onclick="copyCode(this)"><i class="bi bi-clipboard"></i></button></div>');

                return html;
            } catch (e) {
                log('⚠️ 后处理失败: ' + e.message);
                return html;
            }
        }

        // 更新统计信息
        function updateStats(parseTime, sourceLength, resultLength) {
            document.getElementById('parseTime').textContent = parseTime;
            document.getElementById('sourceLength').textContent = sourceLength;
            document.getElementById('resultLength').textContent = resultLength;

            const ratio = (resultLength / sourceLength).toFixed(2);
            document.getElementById('compressionRatio').textContent = ratio + 'x';
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 复制代码功能
        function copyCode(button) {
            try {
                const codeBlock = button.previousElementSibling.querySelector('code');
                const text = codeBlock.textContent;

                navigator.clipboard.writeText(text).then(() => {
                    const originalIcon = button.innerHTML;
                    button.innerHTML = '<i class="bi bi-check"></i>';
                    button.classList.add('btn-success');
                    button.classList.remove('btn-outline-secondary');

                    setTimeout(() => {
                        button.innerHTML = originalIcon;
                        button.classList.remove('btn-success');
                        button.classList.add('btn-outline-secondary');
                    }, 2000);

                    log('✅ 代码已复制到剪贴板');
                }).catch(err => {
                    log('❌ 复制失败: ' + err.message);
                });
            } catch (e) {
                log('❌ 复制代码失败: ' + e.message);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成，开始初始化...');

            setTimeout(() => {
                if (initMarkdownIt()) {
                    log('🎉 初始化成功，可以开始测试');
                    // 自动运行基础测试
                    runTest();
                } else {
                    log('❌ 初始化失败，请检查库文件是否正确加载');
                }
            }, 500);
        });
    </script>
</body>
</html>
