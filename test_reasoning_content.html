<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度思考内容测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.0.10/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.3.1/lib/highlight.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.3.1/styles/github.min.css">
</head>
<body>
    <div class="container mt-4">
        <h2>深度思考内容测试</h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4>模拟流式响应</h4>
                <button class="btn btn-primary" onclick="simulateStreamWithReasoning()">模拟带深度思考的流式响应</button>
                <button class="btn btn-secondary" onclick="simulateStreamWithoutReasoning()">模拟不带深度思考的流式响应</button>
                <button class="btn btn-warning" onclick="clearContent()">清空内容</button>
            </div>
            <div class="col-md-6">
                <h4>视图切换</h4>
                <button class="btn btn-outline-primary" onclick="toggleMarkdownView()">
                    <i id="markdownIcon" class="bi bi-file-text"></i>
                    <span id="markdownText">纯文本</span>
                </button>
            </div>
        </div>
        
        <div class="mt-4">
            <h4>AI分析结果</h4>
            <div id="aiStreamingContent" class="border p-3" style="min-height: 300px; background-color: #f8f9fa;">
                等待内容...
            </div>
        </div>
        
        <div class="mt-3">
            <h5>状态信息</h5>
            <div id="statusInfo" class="alert alert-info">
                <strong>原始内容长度:</strong> <span id="rawContentLength">0</span><br>
                <strong>深度思考内容长度:</strong> <span id="reasoningContentLength">0</span><br>
                <strong>当前视图模式:</strong> <span id="currentViewMode">Markdown</span>
            </div>
        </div>
    </div>

    <script>
        // 模拟AI分析状态
        let aiAnalysisState = {
            isMarkdownView: true,
            rawContent: '',
            reasoningContent: ''
        };

        // 模拟safeMarkdownParse函数
        function safeMarkdownParse(content) {
            try {
                return marked.parse(content);
            } catch (e) {
                console.error('Markdown解析失败:', e);
                return '<pre>' + content.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>';
            }
        }

        function updateStatus() {
            document.getElementById('rawContentLength').textContent = aiAnalysisState.rawContent.length;
            document.getElementById('reasoningContentLength').textContent = aiAnalysisState.reasoningContent.length;
            document.getElementById('currentViewMode').textContent = aiAnalysisState.isMarkdownView ? 'Markdown' : '纯文本';
        }

        function simulateStreamWithReasoning() {
            clearContent();
            
            const normalContent = `# AI分析报告

## 协同创新关系分析

基于年报内容分析，发现以下重要信息：

### 主要发现
1. **技术合作**: 公司与多家高校建立了产学研合作关系
2. **创新投入**: 研发投入占营收比例持续增长
3. **合作伙伴**: 与腾讯、华为等公司在AI领域深度合作

### 投资建议
建议重点关注公司的技术创新能力和合作伙伴关系的发展。`;

            const reasoningContent = `这是一个复杂的分析问题，需要从多个维度考虑：

**数据质量评估**
- 年报数据的完整性和准确性
- 关键词匹配的精确度
- 上下文提取的相关性

**分析方法论**
1. 首先识别所有相关的合作关系
2. 然后评估合作的深度和广度
3. 最后判断对公司价值的影响

**风险因素**
- 合作关系的稳定性
- 技术依赖风险
- 市场竞争变化

**结论置信度**
基于当前数据，分析结论的置信度约为85%，建议结合更多外部数据进行验证。`;

            // 模拟流式响应
            simulateTyping(normalContent, 'ai_chunk', () => {
                setTimeout(() => {
                    simulateTyping(reasoningContent, 'reasoning_chunk', () => {
                        console.log('流式响应完成');
                        updateStatus();
                    });
                }, 500);
            });
        }

        function simulateStreamWithoutReasoning() {
            clearContent();
            
            const normalContent = `# 简单AI分析报告

## 基本分析结果

根据年报数据分析：

- 公司主营业务稳定
- 财务状况良好
- 未发现明显风险

这是一个不包含深度思考内容的简单分析。`;

            simulateTyping(normalContent, 'ai_chunk', () => {
                console.log('简单流式响应完成');
                updateStatus();
            });
        }

        function simulateTyping(content, type, callback) {
            let index = 0;
            const chunkSize = 10;
            
            function typeNext() {
                if (index < content.length) {
                    const chunk = content.slice(index, index + chunkSize);
                    handleStreamData({
                        type: type,
                        data: chunk
                    });
                    index += chunkSize;
                    setTimeout(typeNext, 50);
                } else {
                    if (callback) callback();
                }
            }
            
            typeNext();
        }

        function handleStreamData(data) {
            const streamingContentDiv = document.getElementById('aiStreamingContent');
            
            switch (data.type) {
                case 'ai_chunk':
                    aiAnalysisState.rawContent += data.data;
                    updateDisplay();
                    break;
                    
                case 'reasoning_chunk':
                    aiAnalysisState.reasoningContent += data.data;
                    updateDisplay();
                    break;
            }
            
            updateStatus();
        }

        function updateDisplay() {
            const streamingContentDiv = document.getElementById('aiStreamingContent');
            
            if (aiAnalysisState.isMarkdownView) {
                // 构建完整的markdown内容
                let fullMarkdownContent = aiAnalysisState.rawContent;
                if (aiAnalysisState.reasoningContent) {
                    fullMarkdownContent += '\n\n## 深度思考内容\n\n' + aiAnalysisState.reasoningContent;
                }
                
                const markdownHtml = safeMarkdownParse(fullMarkdownContent);
                streamingContentDiv.innerHTML = markdownHtml;
                streamingContentDiv.classList.add('markdown-content');
            } else {
                // 纯文本视图
                let fullTextContent = aiAnalysisState.rawContent;
                if (aiAnalysisState.reasoningContent) {
                    fullTextContent += '\n\n深度思考内容:\n\n' + aiAnalysisState.reasoningContent;
                }
                streamingContentDiv.textContent = fullTextContent;
                streamingContentDiv.classList.remove('markdown-content');
            }
        }

        function toggleMarkdownView() {
            const markdownIcon = document.getElementById('markdownIcon');
            const markdownText = document.getElementById('markdownText');
            
            aiAnalysisState.isMarkdownView = !aiAnalysisState.isMarkdownView;
            
            if (aiAnalysisState.isMarkdownView) {
                markdownIcon.className = 'bi bi-file-text';
                markdownText.textContent = '纯文本';
            } else {
                markdownIcon.className = 'bi bi-markdown';
                markdownText.textContent = 'Markdown';
            }
            
            updateDisplay();
            updateStatus();
        }

        function clearContent() {
            aiAnalysisState.rawContent = '';
            aiAnalysisState.reasoningContent = '';
            document.getElementById('aiStreamingContent').innerHTML = '等待内容...';
            updateStatus();
        }

        // 初始化
        updateStatus();
    </script>
</body>
</html>
