// 测试markdown修复效果的脚本
// 在浏览器控制台中运行此脚本来测试修复效果

// 测试数据 - 模拟AI分析结果
const testData = {
    simple: `**分析报告：**

**测试公司 (000000) 2024年年报中关联方协同创新情况**

**1. 是否与关联方存在协同创新：**

是的，存在协同创新关系。`,

    withTable: `**分析报告：**

**测试公司 (000000) 2024年年报中关联方协同创新情况**

**1. 是否与关联方存在协同创新：**

是的，存在协同创新关系。

**2. 详细分析表**

**年报主体公司/代码** | **是否存在协同创新** | **原文数据**
测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作，共同推进产品创新。
子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同，提升整体竞争力。
子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目。`,

    complex: `**分析报告：**

**测试公司 (000000) 2024年年报中关联方协同创新情况**

**1. 是否与关联方存在协同创新：**

是的，存在协同创新关系。

**2. 详细分析表**

| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |
|---|---|---|
| 测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作，共同推进产品创新。 |
| 子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同，提升整体竞争力。 |
| 子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目。 |

**3. 协同创新具体表现：**

- **技术研发合作：** 公司与关联方建立联合研发中心
- **资源共享：** 共享研发设备和人才资源  
- **市场协同：** 在产品推广方面形成合力

**4. 总结：**

该公司在关联方协同创新方面表现积极，通过技术合作和资源整合，有效提升了整体创新能力。`
};

// 测试函数
function testMarkdownFix() {
    console.group('🧪 Markdown修复效果测试');
    
    Object.entries(testData).forEach(([name, content]) => {
        console.group(`📝 测试 ${name}`);
        
        try {
            console.log('原始内容长度:', content.length);
            console.log('原始内容预览:', content.substring(0, 100) + '...');
            
            // 测试处理步骤
            const step1 = formatAIContentSimple(content);
            console.log('✅ AI格式化完成，长度:', step1.length);
            
            const step2 = fixTableFormatSimple(step1);
            console.log('✅ 表格修复完成，长度:', step2.length);
            
            const step3 = finalCleanup(step2);
            console.log('✅ 最终清理完成，长度:', step3.length);
            
            // 测试markdown解析
            const parsed = marked.parse(step3);
            console.log('✅ Markdown解析成功，HTML长度:', parsed.length);
            
            // 显示处理后的内容
            console.log('处理后内容预览:');
            console.log(step3.substring(0, 200) + '...');
            
        } catch (e) {
            console.error('❌ 测试失败:', e);
        }
        
        console.groupEnd();
    });
    
    console.groupEnd();
}

// 流式测试函数
function testStreamingParse(content, chunkSize = 50) {
    console.group('🔄 流式解析测试');
    
    let accumulated = '';
    let parseCount = 0;
    
    for (let i = 0; i < content.length; i += chunkSize) {
        const chunk = content.substring(i, i + chunkSize);
        accumulated += chunk;
        
        const shouldParse = shouldParseMarkdownNow(accumulated);
        
        if (shouldParse) {
            parseCount++;
            console.log(`解析 #${parseCount} - 位置: ${i}, 长度: ${accumulated.length}`);
            
            try {
                const parsed = safeMarkdownParse(accumulated);
                console.log('✅ 解析成功');
            } catch (e) {
                console.log('❌ 解析失败:', e.message);
            }
        }
    }
    
    console.log(`总解析次数: ${parseCount}`);
    console.log(`内容长度: ${content.length}`);
    console.log(`平均解析间隔: ${Math.round(content.length / parseCount)} 字符`);
    
    console.groupEnd();
}

// 性能测试函数
function testPerformance() {
    console.group('⚡ 性能测试');
    
    const content = testData.complex;
    const iterations = 100;
    
    // 测试旧方法（如果存在）
    console.time('新方法处理时间');
    for (let i = 0; i < iterations; i++) {
        safeMarkdownParse(content);
    }
    console.timeEnd('新方法处理时间');
    
    // 测试流式格式化
    console.time('流式格式化时间');
    for (let i = 0; i < iterations; i++) {
        formatStreamingText(content);
    }
    console.timeEnd('流式格式化时间');
    
    console.groupEnd();
}

// 错误恢复测试
function testErrorRecovery() {
    console.group('🛡️ 错误恢复测试');
    
    const problematicContent = [
        '```javascript\nfunction test() {\n    console.log("unclosed code block"',
        '| 表格 | 缺少 |\n| 分隔线 |',
        '**未闭合的粗体文本',
        '# 标题\n\n\n\n\n\n过多空行\n\n\n\n',
        '包含\u200B不可见\u200C字符\uFEFF的文本'
    ];
    
    problematicContent.forEach((content, index) => {
        console.log(`测试问题内容 ${index + 1}:`);
        try {
            const result = safeMarkdownParse(content);
            console.log('✅ 成功处理问题内容');
        } catch (e) {
            console.log('❌ 处理失败:', e.message);
        }
    });
    
    console.groupEnd();
}

// 运行所有测试
function runAllTests() {
    console.clear();
    console.log('🚀 开始Markdown修复效果测试');
    
    testMarkdownFix();
    testStreamingParse(testData.complex);
    testPerformance();
    testErrorRecovery();
    
    console.log('✅ 所有测试完成');
}

// 导出测试函数
if (typeof window !== 'undefined') {
    window.testMarkdownFix = testMarkdownFix;
    window.testStreamingParse = testStreamingParse;
    window.testPerformance = testPerformance;
    window.testErrorRecovery = testErrorRecovery;
    window.runAllTests = runAllTests;
    
    console.log('📋 测试函数已加载，可以运行:');
    console.log('- testMarkdownFix() - 基础修复测试');
    console.log('- testStreamingParse(content) - 流式解析测试');
    console.log('- testPerformance() - 性能测试');
    console.log('- testErrorRecovery() - 错误恢复测试');
    console.log('- runAllTests() - 运行所有测试');
}
