<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2><i class="bi bi-broadcast"></i> 流式响应诊断工具</h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试控制</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testSimpleStream()">测试简单流式响应</button>
                        <button class="btn btn-success" onclick="testAIStream()">测试AI流式响应</button>
                        <button class="btn btn-warning" onclick="clearResults()">清空结果</button>
                        <hr>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enableDebug" checked>
                            <label class="form-check-label" for="enableDebug">
                                启用详细调试
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5>连接状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="connectionStatus" class="alert alert-secondary">
                            <i class="bi bi-circle-fill text-secondary"></i> 未连接
                        </div>
                        <small class="text-muted">
                            <strong>浏览器:</strong> <span id="browserInfo"></span><br>
                            <strong>时间:</strong> <span id="currentTime"></span>
                        </small>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>流式响应结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="streamResults" style="height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9em;">
                            等待测试...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>诊断日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="diagnosticLog" style="height: 200px; overflow-y: auto; background-color: #000; color: #00ff00; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.85em;">
                            [系统] 诊断工具已就绪<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let debugEnabled = true;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('diagnosticLog');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                'info': '#00ff00',
                'warn': '#ffff00', 
                'error': '#ff0000',
                'success': '#00ffff'
            };
            
            logDiv.innerHTML += `<span style="color: ${colors[type] || '#00ff00'}">[${timestamp}] ${message}</span><br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateConnectionStatus(status, message) {
            const statusDiv = document.getElementById('connectionStatus');
            const icons = {
                'connecting': '<i class="bi bi-circle-fill text-warning"></i>',
                'connected': '<i class="bi bi-circle-fill text-success"></i>',
                'disconnected': '<i class="bi bi-circle-fill text-danger"></i>',
                'idle': '<i class="bi bi-circle-fill text-secondary"></i>'
            };
            
            statusDiv.innerHTML = `${icons[status] || icons.idle} ${message}`;
            statusDiv.className = `alert alert-${status === 'connected' ? 'success' : status === 'connecting' ? 'warning' : status === 'disconnected' ? 'danger' : 'secondary'}`;
        }
        
        function appendStreamResult(data, type = 'data') {
            const resultsDiv = document.getElementById('streamResults');
            const timestamp = new Date().toLocaleTimeString();
            
            if (type === 'data') {
                resultsDiv.innerHTML += `<span style="color: #007bff;">[${timestamp}] 数据:</span> ${data}<br>`;
            } else if (type === 'error') {
                resultsDiv.innerHTML += `<span style="color: #dc3545;">[${timestamp}] 错误:</span> ${data}<br>`;
            } else if (type === 'status') {
                resultsDiv.innerHTML += `<span style="color: #28a745;">[${timestamp}] 状态:</span> ${data}<br>`;
            }
            
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function testSimpleStream() {
            clearResults();
            log('开始测试简单流式响应', 'info');
            updateConnectionStatus('connecting', '连接中...');
            
            // 创建一个简单的测试请求
            const testData = {
                stock_codes: '000001',
                keywords: '测试',
                prompt: '这是一个流式响应测试',
                openai_config: {}
            };
            
            fetch('/api/ai_analysis_stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                log(`HTTP状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                updateConnectionStatus('connected', '已连接，接收数据中...');
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            log('流式响应结束', 'success');
                            updateConnectionStatus('idle', '连接已关闭');
                            return;
                        }
                        
                        const chunk = decoder.decode(value, { stream: true });
                        buffer += chunk;
                        
                        // 处理完整的行
                        const lines = buffer.split('\n');
                        buffer = lines.pop(); // 保留不完整的行
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    const data = JSON.parse(line.slice(6));
                                    log(`收到数据: ${JSON.stringify(data)}`, 'info');
                                    
                                    if (data.type === 'ai_chunk') {
                                        appendStreamResult(data.data, 'data');
                                    } else if (data.type === 'status') {
                                        appendStreamResult(data.message, 'status');
                                    } else if (data.type === 'debug') {
                                        log(`调试信息: ${data.message}`, 'warn');
                                    } else if (data.type === 'error') {
                                        appendStreamResult(data.message, 'error');
                                    }
                                } catch (e) {
                                    log(`解析数据失败: ${line} - ${e.message}`, 'error');
                                }
                            } else if (line.trim()) {
                                log(`收到非数据行: ${line}`, 'warn');
                            }
                        }
                        
                        return readStream();
                    });
                }
                
                return readStream();
            })
            .catch(error => {
                log(`请求失败: ${error.message}`, 'error');
                updateConnectionStatus('disconnected', '连接失败');
                appendStreamResult(error.message, 'error');
            });
        }
        
        function testAIStream() {
            // 这里可以添加更复杂的AI流式测试
            log('AI流式测试功能待实现', 'warn');
        }
        
        function clearResults() {
            document.getElementById('streamResults').innerHTML = '等待测试...';
            log('清空测试结果', 'info');
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示浏览器信息
            document.getElementById('browserInfo').textContent = navigator.userAgent;
            
            // 更新时间
            function updateTime() {
                document.getElementById('currentTime').textContent = new Date().toLocaleString();
            }
            updateTime();
            setInterval(updateTime, 1000);
            
            // 监听调试开关
            document.getElementById('enableDebug').addEventListener('change', function() {
                debugEnabled = this.checked;
                log(`调试模式: ${debugEnabled ? '开启' : '关闭'}`, 'info');
            });
            
            log('诊断工具初始化完成', 'success');
        });
    </script>
</body>
</html>
