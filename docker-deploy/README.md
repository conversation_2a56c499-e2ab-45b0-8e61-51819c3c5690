# CNinfo年报分析系统 - Docker部署

## 📁 文件结构

```
docker-deploy/
├── app.py                 # Flask主应用
├── web_spider.py         # 爬虫核心逻辑
├── database.py           # 数据库管理
├── run_web.py           # Web启动脚本
├── requirements.txt      # Python依赖
├── Dockerfile           # Docker镜像构建文件
├── docker-compose.yml   # Docker编排文件
├── .dockerignore       # Docker忽略文件
├── templates/
│   └── index.html       # Web界面模板
├── static/
│   ├── css/
│   │   └── style.css    # 样式文件
│   └── js/
│       └── app.js       # 前端JavaScript
└── data/                # 数据持久化目录
    ├── txt/             # TXT文件存储
    ├── pdf/             # PDF文件存储
    └── database/        # SQLite数据库
```

## 🚀 快速部署

### 1. 确保Docker已安装
```bash
docker --version
docker-compose --version
```

### 2. 构建并启动服务
```bash
# 进入部署目录
cd docker-deploy

# 构建并启动容器
docker-compose up -d --build
```

### 3. 查看服务状态
```bash
# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f cninfo-web
```

### 4. 访问应用
```
浏览器访问: http://localhost:5000
```

## 🔧 常用命令

```bash
# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看实时日志
docker-compose logs -f

# 进入容器
docker-compose exec cninfo-web bash

# 更新并重新部署
docker-compose down
docker-compose up -d --build
```

## 📊 数据管理

- **数据持久化**: 所有数据存储在 `./data/` 目录下
- **备份数据**: 直接复制 `data` 文件夹即可
- **迁移数据**: 将现有的txt、pdf、database文件复制到对应的data子目录

## 🔍 故障排除

1. **端口冲突**: 修改 docker-compose.yml 中的端口映射
2. **权限问题**: 确保data目录有读写权限
3. **内存不足**: 增加Docker的内存限制

## 📝 注意事项

- 首次启动可能需要几分钟来下载依赖
- 数据目录会自动创建，无需手动操作
- 容器会自动重启，除非手动停止
