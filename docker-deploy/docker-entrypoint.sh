#!/bin/bash

echo "🐳 CNinfo年报分析系统 - Docker容器启动"
echo "================================================"
echo "📅 启动时间: $(date)"
echo "📁 工作目录: $(pwd)"
echo ""

# 检查数据目录
echo "📂 检查数据目录..."
ls -la data/ 2>/dev/null || echo "⚠️ data目录不存在"

# 检查数据库文件
if [ -f "data/database/cninfo_reports.db" ]; then
    echo "✅ 发现数据库文件: data/database/cninfo_reports.db"
    echo "📊 数据库大小: $(du -h data/database/cninfo_reports.db | cut -f1)"
else
    echo "❌ 未找到数据库文件: data/database/cninfo_reports.db"
fi

# 检查TXT文件
if [ -d "data/txt" ]; then
    txt_count=$(find data/txt -name "*.txt" | wc -l)
    echo "📄 TXT文件数量: $txt_count"
else
    echo "📁 TXT目录不存在"
fi

echo ""
echo "🚀 启动Flask应用..."
echo "================================================"

# 启动Python应用
exec python -u run_web.py
