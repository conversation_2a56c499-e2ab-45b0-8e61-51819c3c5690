# 🚀 巨潮年报爬虫工具 - 快速启动指南

## 📥 获取程序

### 方式一：直接使用（推荐）
1. 下载 `CNInfo_Crawler_Optimized.exe`
2. 双击运行，无需安装任何环境

### 方式二：Python源码运行
1. 确保已安装 Python 3.8+
2. 运行依赖检查：`python check_dependencies.py`
3. 启动程序：`python cninfo_gui_ctk.py`

## 🎯 5分钟上手

### 第1步：启动程序
- 双击 `CNInfo_Crawler_Optimized.exe`
- 程序会在左上角启动

### 第2步：输入参数
```
📈 公司股票代码（左上角）:
300454
000001
600036

🔍 搜索关键词（右上角）:
年度报告

📅 时间范围:
开始日期: 2024-01-01
结束日期: 2025-12-31

📊 统计关键词（下方）:
人工智能
大数据
云计算
区块链
```

### 第3步：开始爬取
1. 点击 **"开始爬取"** 按钮
2. 程序自动滚动到日志区域显示进度
3. 等待爬取完成

### 第4步：查看结果
1. 点击 **"查看结果"** 查看数据表格
2. 点击 **"打开结果文件夹"** 查看Excel文件
3. 点击 **"历史记录"** 查看所有历史结果

## 📁 结果文件说明

程序会自动生成以下文件：

```
results/
├── excel/
│   ├── company_single_pdf.xls    # 搜索到的PDF列表
│   └── company_keywords.xlsx     # 关键词统计结果
├── history/                      # 历史记录（按时间戳保存）
├── pdf/                         # 下载的PDF文件
└── txt/                         # 提取的文本文件
```

## ⚡ 常用功能

| 按钮 | 功能 |
|------|------|
| 开始爬取 | 启动任务并自动滚动到日志 |
| 停止 | 中断当前任务 |
| 查看结果 | 在窗口中显示数据表格 |
| 打开结果文件夹 | 查看生成的Excel文件 |
| 历史记录 | 查看所有历史查询结果 |
| 清空日志 | 清除运行日志 |

## 💡 使用技巧

1. **批量查询**: 在股票代码框输入多个代码，每行一个
2. **关键词搜索**: 支持中文关键词，如"年度报告"、"半年度报告"
3. **时间范围**: 2024年年报通常在2025年3-4月发布
4. **结果查看**: 使用内置表格查看器，支持排序和筛选
5. **历史管理**: 所有结果自动保存，支持历史查询

## ❗ 注意事项

- 🌐 需要稳定的网络连接
- ⏱️ 程序内置延时，避免请求过频
- 💾 确保有足够的磁盘空间存储PDF
- 🔒 部分杀毒软件可能误报，请添加信任

## 🆘 遇到问题？

1. **程序无法启动**: 检查杀毒软件是否拦截
2. **网络连接失败**: 检查网络和防火墙设置
3. **下载失败**: 检查磁盘空间和文件夹权限
4. **依赖缺失**: 运行 `python check_dependencies.py`

## 📞 获取帮助

- 查看完整文档：`README.md`
- 运行环境检查：`python compatibility_check.py`
- 查看使用说明：`使用说明_完整版.md`

---

**快速上手只需5分钟！** 🎉
