<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能调试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Markdown-it 库 -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@13.0.2/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-table@2.0.0/dist/markdown-it-table.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-attrs@4.1.6/dist/markdown-it-attrs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-container@3.0.0/dist/markdown-it-container.min.js"></script>
    
    <!-- 代码高亮 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/styles/github.min.css">
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.8.0/highlight.min.js"></script>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="bi bi-bug"></i> 功能调试页面</h1>
                <p class="text-muted">测试各种功能是否正常工作</p>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> 功能测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>基础功能测试</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="testBasicFunction()">
                                        <i class="bi bi-play"></i> 测试基础功能
                                    </button>
                                    <button class="btn btn-info" onclick="testMarkdownIt()">
                                        <i class="bi bi-markdown"></i> 测试Markdown-it
                                    </button>
                                    <button class="btn btn-warning" onclick="testLibraryLoading()">
                                        <i class="bi bi-book"></i> 测试库加载状态
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>模拟AI分析功能</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" onclick="simulateAIAnalysis()">
                                        <i class="bi bi-robot"></i> 模拟AI分析
                                    </button>
                                    <button class="btn btn-danger" onclick="testDiagnoseFunction()">
                                        <i class="bi bi-search"></i> 测试诊断功能
                                    </button>
                                    <button class="btn btn-secondary" onclick="testFixFunction()">
                                        <i class="bi bi-magic"></i> 测试修复功能
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-terminal"></i> 控制台输出</h6>
                    </div>
                    <div class="card-body">
                        <div id="consoleOutput" class="bg-dark text-light p-3 rounded" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                            等待测试开始...
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6><i class="bi bi-eye"></i> 测试结果显示</h6>
                    </div>
                    <div class="card-body">
                        <div id="testResults" class="border rounded p-3" style="min-height: 200px;">
                            测试结果将在这里显示...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 模拟AI分析状态
        let aiAnalysisState = {
            rawContent: `**分析报告：**

**测试公司 (000000) 2024年年报中关联方协同创新情况**

**1. 是否与关联方存在协同创新：**

是的，存在协同创新关系。

**2. 详细分析表**

**年报主体公司/代码** | **是否存在协同创新** | **原文数据**
测试公司/000000 | 是 | 公司与关联方在技术研发方面开展深度合作，共同推进产品创新。
子公司A/000001 | 是 | 在人工智能领域与母公司形成技术协同，提升整体竞争力。
子公司B/000002 | 否 | 主要从事传统业务，暂无协同创新项目。

**3. 协同创新具体表现：**

- **技术研发合作：** 公司与关联方建立联合研发中心
- **资源共享：** 共享研发设备和人才资源  
- **市场协同：** 在产品推广方面形成合力

**4. 总结：**

该公司在关联方协同创新方面表现积极，通过技术合作和资源整合，有效提升了整体创新能力。这是一个非常长的段落，用于测试段落分割功能。公司与多家关联方建立了长期稳定的合作关系，在研发、生产、销售等多个环节实现了深度协同。特别是在技术研发领域，公司与关联方共同投入资源，建立联合研发中心，开展前沿技术攻关，取得了显著成效。同时，公司还通过资源共享机制，与关联方实现了设备、人才、信息等资源的优化配置，提高了整体运营效率。`,
            reasoningContent: '',
            isMarkdownView: true
        };

        let markdownRenderer = null;

        // 日志函数
        function log(message) {
            const output = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        // 初始化markdown-it
        function initMarkdownRenderer() {
            if (markdownRenderer) {
                return true;
            }
            
            if (typeof markdownit === 'undefined') {
                log('❌ markdown-it库未加载');
                return false;
            }
            
            try {
                log('🔧 初始化markdown-it解析器...');
                
                markdownRenderer = markdownit({
                    html: true,
                    breaks: true,
                    linkify: true,
                    typographer: false,
                    quotes: '""''',
                    highlight: function (str, lang) {
                        if (typeof hljs !== 'undefined' && lang && hljs.getLanguage(lang)) {
                            try {
                                return '<pre class="hljs"><code>' +
                                       hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +
                                       '</code></pre>';
                            } catch (__) {}
                        }
                        return '<pre class="hljs"><code>' + escapeHtml(str) + '</code></pre>';
                    }
                });

                if (typeof markdownItTable !== 'undefined') {
                    markdownRenderer.use(markdownItTable);
                    log('✅ 表格插件已启用');
                }

                markdownRenderer.renderer.rules.table_open = function() {
                    return '<div class="table-responsive"><table class="table table-striped table-bordered">\n';
                };

                markdownRenderer.renderer.rules.table_close = function() {
                    return '</table></div>\n';
                };

                log('✅ markdown-it解析器初始化完成');
                return true;
                
            } catch (e) {
                log('❌ markdown-it初始化失败: ' + e.message);
                return false;
            }
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 测试基础功能
        function testBasicFunction() {
            log('🧪 开始测试基础功能...');
            
            try {
                // 测试基本的JavaScript功能
                const testArray = [1, 2, 3, 4, 5];
                const result = testArray.map(x => x * 2);
                log('✅ 基础JavaScript功能正常: ' + result.join(', '));
                
                // 测试DOM操作
                const testDiv = document.createElement('div');
                testDiv.textContent = '测试内容';
                log('✅ DOM操作功能正常');
                
                // 测试事件处理
                testDiv.addEventListener('click', function() {
                    log('✅ 事件处理功能正常');
                });
                
                log('✅ 基础功能测试完成');
                
            } catch (e) {
                log('❌ 基础功能测试失败: ' + e.message);
            }
        }

        // 测试库加载状态
        function testLibraryLoading() {
            log('📋 检查库加载状态...');
            
            const libraries = {
                'Bootstrap': typeof bootstrap !== 'undefined',
                'markdown-it': typeof markdownit !== 'undefined',
                'markdown-it-table': typeof markdownItTable !== 'undefined',
                'markdown-it-attrs': typeof markdownItAttrs !== 'undefined',
                'markdown-it-container': typeof markdownItContainer !== 'undefined',
                'highlight.js': typeof hljs !== 'undefined'
            };

            Object.entries(libraries).forEach(([name, loaded]) => {
                log(`${name}: ${loaded ? '✅' : '❌'}`);
            });
        }

        // 测试markdown-it
        function testMarkdownIt() {
            log('🧪 开始测试markdown-it...');
            
            if (!initMarkdownRenderer()) {
                log('❌ markdown-it初始化失败');
                return;
            }

            const testContent = `# 测试标题

这是一个**粗体**文本测试。

| 列1 | 列2 |
|---|---|
| 数据1 | 数据2 |`;

            try {
                const result = markdownRenderer.render(testContent);
                log('✅ markdown-it解析成功');
                
                document.getElementById('testResults').innerHTML = result;
                
            } catch (e) {
                log('❌ markdown-it解析失败: ' + e.message);
            }
        }

        // 模拟AI分析
        function simulateAIAnalysis() {
            log('🤖 开始模拟AI分析...');
            
            if (!initMarkdownRenderer()) {
                log('❌ markdown-it未初始化，无法进行分析');
                return;
            }

            try {
                const result = markdownRenderer.render(aiAnalysisState.rawContent);
                log('✅ AI分析内容解析成功');
                
                document.getElementById('testResults').innerHTML = result;
                
            } catch (e) {
                log('❌ AI分析内容解析失败: ' + e.message);
            }
        }

        // 测试诊断功能
        function testDiagnoseFunction() {
            log('🔍 开始测试诊断功能...');
            
            // 这里我们需要复制一些诊断逻辑
            const content = aiAnalysisState.rawContent;
            const issues = [];
            
            // 检查表格
            if (content.includes('|')) {
                const tableLines = content.split('\n').filter(line => line.includes('|'));
                const hasSeparator = tableLines.some(line => line.match(/^\|[\s\-|]+\|$/));
                if (!hasSeparator) {
                    issues.push('表格缺少分隔线');
                }
            }
            
            // 检查粗体文本
            const boldTitles = content.match(/\*\*[^*]+\*\*/g) || [];
            if (boldTitles.length > 5) {
                issues.push('过多粗体文本');
            }
            
            // 检查长段落
            const paragraphs = content.split('\n\n');
            const longParagraphs = paragraphs.filter(p => p.length > 300);
            if (longParagraphs.length > 0) {
                issues.push(`存在 ${longParagraphs.length} 个过长段落`);
            }
            
            log(`🔍 诊断完成，发现 ${issues.length} 个问题:`);
            issues.forEach((issue, index) => {
                log(`${index + 1}. ${issue}`);
            });
            
            if (issues.length === 0) {
                log('✅ 没有发现问题');
            }
        }

        // 测试修复功能
        function testFixFunction() {
            log('🔧 开始测试修复功能...');
            
            try {
                // 简单的修复逻辑
                let fixed = aiAnalysisState.rawContent;
                
                // 修复表格
                fixed = fixed.replace(/\*\*年报主体公司[\/\\]代码\*\*\s*\|\s*\*\*是否存在协同创新\*\*\s*\|\s*\*\*原文数据\*\*/g, 
                    '\n| 年报主体公司/代码 | 是否存在协同创新 | 原文数据 |\n|---|---|---|');
                
                // 修复粗体标题
                fixed = fixed.replace(/\*\*([^*]*分析报告[^*]*)\*\*/g, '\n# $1\n');
                fixed = fixed.replace(/\*\*([^*]{3,50})[：:]\s*\*\*/g, '\n## $1\n');
                
                log('✅ 修复处理完成');
                
                if (markdownRenderer) {
                    const result = markdownRenderer.render(fixed);
                    document.getElementById('testResults').innerHTML = result;
                    log('✅ 修复后内容渲染成功');
                } else {
                    log('❌ markdown-it未初始化，无法渲染修复后的内容');
                }
                
            } catch (e) {
                log('❌ 修复功能测试失败: ' + e.message);
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 页面加载完成');
            
            setTimeout(() => {
                log('⏰ 延迟初始化开始...');
                testLibraryLoading();
                
                if (initMarkdownRenderer()) {
                    log('🎉 初始化成功，可以开始测试');
                } else {
                    log('❌ 初始化失败');
                }
            }, 500);
        });
    </script>
</body>
</html>
